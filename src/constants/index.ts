// 部门UC权限过滤
export const UC_RBAC_DEPT_FILTER = { prefix: 'admin/staff_relation', code: '9900' }

// oss上传配置
export const OSS_CONFIG = {
  service: '/admin/staff_relation',
  module: 'staff_relation',
}

// 异步任务类型
export enum EXPORT_TASK_TYPE {
  SECURITY = 1,
  EMPLOY_DISPUTE = 2,
}

export const CLIENT_CODES = {
  antdPrefixCls: 'staff-relation',
}

// 外部使用页面路由
export const EXTERNAL_USE_PAGE_ROUTE = ['/employment-dispute-detail']

/** 是否微应用 */
export const isMicroApp = (function () {
  return !!window.__POWERED_BY_QIANKUN__
})()

// UC字典配置
export const UC_DICT_CONFIG = {
  appService: 'admin/staff_relation',
}

// UC 权限项配置
export const UC_DICT_KEY = {
  // UC应用合并，修改使用主应用的权限配置编码
  SECURITY_AUTH_FIELD: '4101210003', // 安全事件字段编辑权限
  EMPLOYMENT_DISPUTE_AUTH_FIELD: '4101210004', // 用工争议字段编辑权限
  BM_HTZT: 'BM_HTZT', // 合同主体
  BM_BZGSQY: 'BM_BZGSQY', // 编制归属区域属性
  DEPT_AUTH: '4101210005', // 	部门查询权限
}

// 付款状态
export enum PAYMENT_STATUS {
  approved = 'ERS_PS_003',
  inReview = 'ERS_PS_002',
  notApproved = 'ERS_PS_004',
  notStarted = 'ERS_PS_001',
}

// table Tooltip 配置
export const TABLE_TOOLTIP_ELLIPSIS = {
  tooltipProps: {
    overlayStyle: { overflow: 'hidden' },
    overlayInnerStyle: { whiteSpace: 'pre-wrap', maxHeight: 300, overflow: 'auto' },
  },
}

// 安全事件-进度
export const SECURITY_EVENT_PROCESS = {
  // 跟进中-1
  inProgress: '1',
  // 已撤案-2
  withdrawn: '2',
  // 已结案-3
  closed: '3',
}

export const SECURITY_EVENT_PROCESS_COLOR = {
  [SECURITY_EVENT_PROCESS.inProgress]: 'blue',
  [SECURITY_EVENT_PROCESS.closed]: 'green',
}

export const YES_NO_OPTIONS = [
  {
    label: '是',
    value: '1',
  },
  {
    label: '否',
    value: '0',
  },
]

// 安全事件-未结案原因隐藏字典项
// 【仲裁/诉讼中】、【医疗期】、【医疗期(骨折)】
export const CLOSE_CASE_HIDE_VALUE = ['07', '08', '14']

export const processTagMap = {
  [SECURITY_EVENT_PROCESS.inProgress]: '#1890FF',
  [SECURITY_EVENT_PROCESS.closed]: '#52C41A',
  [SECURITY_EVENT_PROCESS.withdrawn]: '#9F9F9F',
}
