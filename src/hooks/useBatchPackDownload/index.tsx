import JSZip from 'jszip'
import { saveAs } from 'file-saver'
import { message } from 'antd'
import { uploadSdk } from '@/services/upload'
import { map } from 'lodash-es'
import { useLoaderContext } from '@/components/Loader/useLoading'
import moment from 'moment'

export type BatchPackDownloadParams = {
  uuid: string
  name: string
}

const useBatchPackDownload = () => {
  const { loader } = useLoaderContext()

  const batchPackDownload = (fileArr: BatchPackDownloadParams[], fileName?: string) => {
    loader?.show('正在打包下载中...')
    Promise.all(
      map(fileArr, (item) => {
        return uploadSdk.download(item.uuid as string)
      }),
    )
      .then((datas) => {
        const fileLinks = map(fileArr, (item, index) => ({
          ...item,
          url: datas[index].url,
        }))
        const zip = new JSZip()
        // 使用 Promise.all 来异步获取文件内容
        Promise.all(
          fileLinks.map((file) =>
            fetch(file.url)
              .then((response) => response.arrayBuffer())
              .then((buffer) => zip.file(file.name, new Uint8Array(buffer))),
          ),
        )
          .then(() => {
            // 生成压缩文件
            return zip.generateAsync({ type: 'blob' })
          })
          .then(function (content) {
            // 使用 FileSaver.js 触发文件下载
            saveAs(content, `${fileName || `文件_${moment().valueOf()}`}.zip`)
            loader?.hide()
            message.success('打包下载成功')
          })
          .catch(() => {
            loader?.hide()
            message.error('打包下载失败')
          })
      })
      .catch(() => {
        loader?.hide()
        message.error('打包下载失败')
      })
  }
  return { batchPackDownload }
}

export default useBatchPackDownload
