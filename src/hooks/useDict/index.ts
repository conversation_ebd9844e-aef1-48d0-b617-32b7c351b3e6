import { useQuery } from 'react-query'
import { service } from '@galaxy/dict'
import { fromPairs, keys, map } from 'lodash-es'
import config from '@/config'

const { tenantId } = config

// EHR 码表
const EhrDictKey = [
  'BM_BZGSQY', // 案发区域：安全事件
  'BM_VAA', // 案发合同类型：安全事件、用工争议
  'BM_AJXS', // 案件系数：安全事件
  'BM_PeriodicClass', // 跟进周期分类：安全事件
  'BM_INJURYSTATUS', // 受伤人员情况：安全事件
  // 'BM_POSTTYPE', // 岗位类型划分：安全事件
  'BM_PostType', // 岗位类型划分：安全事件
  'BM_RESPONSIBILITY', // 责任划分：安全事件
  'BM_WOUNDTYPE', // 员工受伤类型：安全事件
  'BX_AJJD', // 进度：安全事件
  'BM_73', // 案发区域（OA）：安全事件、用工争议
  'BM_REPORT_CO', // 申报单位：安全事件
  'BX_SFXZ', // 申报险种：安全事件
  'BM_CANCELREASON', // 撤案原因：安全事件
  'BM_DuringtimeType', // 跟进时长分类：安全事件
  'BM_CLOSE_CASE', // 未结案原因：安全事件
  'BM_PAYMENTCO', // 实际赔付单位：安全事件
  'BM_PAY_CATEGORY', // 赔付险种：安全事件
  'BM_REPAYSCHE', // 还款进度：安全事件
  'BM_56', // 奖惩类型：安全事件
  'BM_249', // 奖惩进度：安全事件
  'BM_250', // 未实行奖惩原因：安全事件
  'BM_NoPunishReason', // 不处罚原因：安全事件
  'BM_INJURE_DEGREE', // 伤残等级：安全事件
  'BM_OCCURSENCE', // 案发场景： 安全事件
  'BM_35', // 用工形式：用工争议
  'BM_KK', // 人员类别：用工争议
  'BM_HTZT', // 合同主体：用工争议
  'BM_event_type', // 事件类型（需取一二级）：用工争议
  'BM_institution', // 受理渠道：用工争议
  'BM_solution', // 现阶段进程：用工争议
  'BM_result', // 结果：用工争议
  // 'BM_DISCHARGETYPE', // 解除用工关系类型：用工争议
  'BM_Dischargetype', // 解除用工关系类型：用工争议
  'BM_labor_type_pay', // 事件类别（薪酬）：用工争议
  'BM_pay_state', // 付款结果：用工争议
]

// DHR 员工关系字典
const DHRDictKey = [
  'THE_ACCIDENT_NATURE', // 事故性质：安全事件
  'THE_CRIME_SCENE', // 案发场景：安全事件
  'TIME_CLASSIFICATION', // 时间分类：安全事件
  'EVENT_CLASSIFICATION', // 事件分类：安全事件
  'EVENT_LEVEL', // 事件等级：安全事件
  'TYPE_OF_PENALTY', // 处罚类型：安全事件
  'THE_WEATHER', // 天气：安全事件
  'REWARD_PUNISHMENT_CATEGORIES', // 奖惩类别：安全事件
  'SUBJECT_OF_ARBITRATION', // 起诉/仲裁主体：用工争议
  'PAYMENT_STATUS', // 用工争议付款状态：用工争议
]
const TREE_KEY = ['BM_event_type']

// 树
const listToTree = (data, id = 'id', parentId = 'parentId', children = 'children', rootId = 0) => {
  const cloneData = JSON.parse(JSON.stringify(data))
  const treeData = cloneData.filter((item) => {
    const father = item
    const branchArr = cloneData.filter((child) => father[id] === child[parentId])
    father[children] = branchArr.length > 0 ? branchArr : null
    return Number(father[parentId]) === rootId
  })
  return treeData.length !== 0 ? treeData : data
}

// 处理字典数据
const solveDictListData = (res) => {
  const dictData: Record<string, any> = {}
  map(keys(res), (key) => {
    // 对BM_PAYMENTCO、BM_PAY_CATEGORY数据进行特殊处理
    const disabledMap = {
      BM_PAYMENTCO: ['5', '7', '10'],
      BM_PAY_CATEGORY: ['11', '12', '06'],
    }

    const list = map(res[key] || [], (item) => {
      const isEnabled = disabledMap[key]?.includes(item.code) ? false : item.isEnabled

      return {
        ...item,
        label: item.name,
        value: item.code,
        id: item.id,
        parentId: item.parentId,
        isEnabled,
      }
    })
    dictData[key] = {
      list,
      tree: TREE_KEY.includes(key) ? listToTree(list) : undefined,
      enums: fromPairs(map(res[key] || [], (subItem) => [subItem.code, subItem.name])),
    }
  })
  return dictData
}

// 获取EHR字典
export function useEhrDicts() {
  return useQuery(
    'EHRDict',
    async () => {
      const res = await service.getDictionaryItem(EhrDictKey, 'EHR', {
        containsGlobal: false,
        tenantId: `${tenantId}`,
        isEnabled: undefined,
      })
      return solveDictListData(res)
    },
    {
      cacheTime: Infinity,
      staleTime: Infinity,
      retry: false,
    },
  )
}

// 获取DHR员工关系字典
export function useDhrDicts() {
  return useQuery(
    'DHRYGDict',
    async () => {
      const res = await service.getDictionaryItem(DHRDictKey, 'staff_relation', {
        containsGlobal: false,
        tenantId: `${tenantId}`,
        isEnabled: undefined,
      })
      return solveDictListData(res)
    },
    {
      cacheTime: Infinity,
      staleTime: Infinity,
      retry: false,
    },
  )
}
