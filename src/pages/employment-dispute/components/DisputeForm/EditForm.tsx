import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import React, { useEffect, useMemo, useState } from 'react'
import { Row, Col, Card, Button } from 'antd'
import { Anchor as DhrAnchor } from '@amazebird/editor-component'
import { useStateCenter } from '@amazebird/schema-form'
import '@amazebird/antd-business-field'
import { IUcPermissionData } from '@/hooks/useUcPermissionDict'
import { EMPLOYMENT_DISPUTE } from '@/constants/rbac-code/employment-dispute'
import BusinessPermission from '@/components/businessPermission'
import { UC_DICT_KEY } from '@/constants'
import { keys, map } from 'lodash-es'
import { SchemaConfigType, getSchema } from './schema'
import { generateFormItemConfig } from './utils'
import styles from './index.less'

type IProps = {
  form?: any
  ucDict?: IUcPermissionData
  ehrDict?: Record<string, any>
  dhrDict?: Record<string, any>
  batchPackDownload?: () => void
}

export default function EditForm(props: IProps) {
  const { ehrDict, dhrDict, ucDict, batchPackDownload, ...rest } = props
  const ctx = useStateCenter()
  const [hasAttachment, setHasAttachment] = useState(false)
  // 人员组织选择器 展示组织树结构权限
  const hasIsSpecialAuth = useMemo(() => {
    const authFields = map(
      ucDict?.[UC_DICT_KEY.EMPLOYMENT_DISPUTE_AUTH_FIELD] || [],
      (subItem) => subItem.code,
    )
    return authFields.includes('isSpecial')
  }, [ucDict])

  const basicProps = {
    labelCol: { flex: '130px' },
    wrapperCol: { span: 14 },
  }
  const schema = useMemo(() => getSchema(SchemaConfigType.Edit, { ucDict }), [])
  useEffect(() => {
    if (ehrDict) {
      ctx.setState({
        ehrDict,
      })
    }
    if (dhrDict) {
      ctx.setState({
        dhrDict,
      })
    }
  }, [ehrDict, dhrDict])

  useEffect(() => {
    const values = props.form.getFieldsValue()
    setHasAttachment(values?.attachment?.length > 0)
  }, [])

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      if (changedValues[key] && changedValues[key].length > 0 && !hasAttachment) {
        setHasAttachment(true)
      } else if ((!changedValues[key] || changedValues[key].length <= 0) && hasAttachment) {
        setHasAttachment(false)
      }
    }
  }

  return (
    <SchemaForm
      {...rest}
      schema={schema}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 10 }}
      onValuesChange={onValuesChange}
      className={styles.disputeForm}
    >
      <Row>
        <Col span="24">
          <div className={styles.basicInfo}>
            <Row gutter={0} align="top">
              <Col span={6}>
                <Item {...generateFormItemConfig('departmentFullName')} {...basicProps} />
              </Col>
              <Col span={6}>
                <Item {...generateFormItemConfig('firstLevelDepartment')} {...basicProps} />
              </Col>
              <Col span={6}>
                <Item {...generateFormItemConfig('secondLevelDepartment')} {...basicProps} />
              </Col>
              <Col span={6}>
                <Item {...generateFormItemConfig('thirdLevelDepartment')} {...basicProps} />
              </Col>
            </Row>
            <Row gutter={0} align="top">
              <Col span={6}>
                <Item {...generateFormItemConfig('fourthLevelDepartment')} {...basicProps} />
              </Col>
              <Col span={6}>
                <Item {...generateFormItemConfig('positionName')} {...basicProps} />
              </Col>
              <Col span={6}>
                <Item {...generateFormItemConfig('empFormName')} {...basicProps} />
              </Col>
              <Col span={6}>
                <Item {...generateFormItemConfig('empCategoryName')} {...basicProps} />
              </Col>
            </Row>
            <Row gutter={0} align="top">
              <Col span={6}>
                <Item {...generateFormItemConfig('contractTypeName')} {...basicProps} />
              </Col>
              <Col span={6}>
                <Item {...generateFormItemConfig('userNameUpdate')} {...basicProps} />
              </Col>
              <Col span={6}>
                <Item {...generateFormItemConfig('timeUpdate')} {...basicProps} />
              </Col>
              <Col span={6}>
                <Item {...generateFormItemConfig('oaNo')} {...basicProps} />
              </Col>
            </Row>
            <Row gutter={0} align="top">
              <Col span={6} className={styles.lastCol}>
                <Item {...generateFormItemConfig('disputeId')} {...basicProps} />
              </Col>
              <Col span={6} />
              <Col span={6} />
              <Col span={6} />
            </Row>
          </div>
        </Col>
        <Col span="24">
          <div className={styles.anchorForm}>
            <DhrAnchor
              options={[
                { title: '案件信息', message: '' },
                { title: '案件进程', message: '' },
                { title: '案件结果', message: '' },
                { title: '案件金额' },
                { title: '附件' },
              ]}
              className={styles.anchorContainer}
            >
              <Card title="案件信息" size="default">
                <>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('arbitrationSubject')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('subjectName')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('timeEvent')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('timeIncidentYearMonth')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('eventLocationId')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('isDepartmentDuty')} />
                    </Col>
                  </Row>
                  <Item
                    {...generateFormItemConfig('deptResponsibleSituation')}
                    wrapperCol={{ span: 17 }}
                    labelCol={{ span: 4 }}
                  />
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('eventTypeLevelFirstId')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('eventTypeLevelSecondId')} />
                    </Col>
                  </Row>
                  <Item
                    {...generateFormItemConfig('eventContent')}
                    wrapperCol={{ span: 17 }}
                    labelCol={{ span: 4 }}
                  />
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('isSafetyEventUpgrade')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('crisisReportRelation')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    {hasIsSpecialAuth && (
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isSpecial')} />
                      </Col>
                    )}

                    <Col span={12}>
                      <Item {...generateFormItemConfig('otherEventTypeDesc')} />
                    </Col>
                  </Row>
                  <Item
                    {...generateFormItemConfig('remark')}
                    wrapperCol={{ span: 17 }}
                    labelCol={{ span: 4 }}
                  />
                </>
              </Card>
              <Card title="案件进程" size="default">
                <>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('sscHandler')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('acceptChannelId')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('currentStageProcessId')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('timeEventClosure')} />
                    </Col>
                  </Row>
                  <Item
                    {...generateFormItemConfig('progressDetails')}
                    wrapperCol={{ span: 17 }}
                    labelCol={{ span: 4 }}
                  />
                </>
              </Card>
              <Card title="案件结果" size="default">
                <>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('eventResultId')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('eventNum')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('eventResultDetails')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('dataSource')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('terminationTypeId')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('isEconCompInvolvement')} />
                    </Col>
                  </Row>
                </>
              </Card>
              <Card title="案件金额" size="default">
                <>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('empClaimAmount')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('eventClosureAmount')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('companyBearingAmount')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('empBearingAmount')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('commercialInsuranceAmount')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('statutoryStandardAmount')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('eventCategorySalaryId')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('isRequiredPayment')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('payAmount')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('timeScheduledPayment')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('timeActualPayment')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('paymentResult')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('paymentEntity')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('payeeName')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('payeeBank')} />
                    </Col>
                    <Col span={12}>
                      <Item {...generateFormItemConfig('payeeBankAccount')} />
                    </Col>
                  </Row>
                  <Row gutter={0} align="top">
                    <Col span={12}>
                      <Item {...generateFormItemConfig('payeeInterBankNo')} />
                    </Col>
                    <Col span={12} />
                  </Row>
                </>
              </Card>
              <Card
                title="附件"
                size="default"
                extra={
                  <BusinessPermission code={EMPLOYMENT_DISPUTE.BATCH_DOWNLOAD}>
                    <Button
                      type="link"
                      disabled={!hasAttachment}
                      onClick={() => {
                        batchPackDownload?.()
                      }}
                    >
                      打包下载
                    </Button>
                  </BusinessPermission>
                }
              >
                <Item {...generateFormItemConfig('attachment')} />
              </Card>
            </DhrAnchor>
          </div>
        </Col>
      </Row>
    </SchemaForm>
  )
}
