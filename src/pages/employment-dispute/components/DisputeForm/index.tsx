import React from 'react'
import { renderStateCenterCtx, StateCenter } from '@amazebird/utils'
import { IUcPermissionData } from '@/hooks/useUcPermissionDict'
import EditForm from './EditForm'
import DetailForm from './DetailForm'

type IProps = {
  form?: any
  ucDict?: IUcPermissionData
  ehrDict?: Record<string, any>
  dhrDict?: Record<string, any>
  batchPackDownload?: () => void
}

const bindData = [
  {
    name: 'DisputeForm',
    fields: [
      'arbitrationSubject',
      'subjectName',
      'timeEvent',
      'timeIncidentYearMonth',
      'eventLocation',
      'isDepartmentDuty',
      'deptResponsibleSituation',
      'eventTypeLevelFirstId',
      'eventTypeLevelSecondId',
      'eventContent',
      'isSafetyEventUpgrade',
      'crisisReportRelation',
      'isSpecial',
      'otherEventTypeDesc',
      'remark',
      'sscHandler',
      'acceptChannelId',
      'currentStageProcessId',
      'timeEventClosure',
      'progressDetails',
      'eventResultId',
      'eventNum',
      'eventResultDetails',
      'dataSource',
      'terminationTypeId',
      'isEconCompInvolvement',
      'employeeClaimAmount',
      'eventClosureAmount',
      'companyBearingAmount',
      'employeeBearingAmount',
      'commercialInsuranceAmount',
      'statutoryStandardAmount',
      'eventCategorySalaryId',
      'isRequiredPayment',
      'payAmount',
      'timeScheduledPayment',
      'timeActualPayment',
      'payResult',
      'paymentEntity',
      'payeeName',
      'payeeBank',
      'payeeBankAccount',
      'payeeInterBankNo',
      'attachment',
    ],
  },
]

export function DisputeForm(props: IProps) {
  const initialStates = { dhrDict: null, ehrDict: null }

  const stateCenter = new StateCenter({
    initialStates,
    bindData,
  })

  return renderStateCenterCtx(stateCenter, <EditForm {...props} />)
}

export function DisputeDetailForm(props: IProps) {
  const initialStates = { dhrDict: null, ehrDict: null }

  const stateCenter = new StateCenter({
    initialStates,
    bindData,
  })

  return renderStateCenterCtx(stateCenter, <DetailForm {...props} />)
}
