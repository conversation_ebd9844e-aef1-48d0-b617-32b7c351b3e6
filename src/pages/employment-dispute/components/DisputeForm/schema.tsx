import React from 'react'
import { Observer, SchemaType, registerComponent } from '@amazebird/antd-schema-form'
import { Typography } from 'antd'
import moment from 'moment'
import { filter, findIndex, isNil, isObject, keys, map } from 'lodash-es'
import { TreeSelect as OrgSelector } from '@galaxy/org-selector'
import { TreeSelect as UserSelector } from '@galaxy/user-selector'
import { OSS_CONFIG, UC_DICT_KEY } from '@/constants'
import { isMobile } from '@galaxy/utils'

export enum SchemaConfigType {
  Edit = 'edit',
  Detail = 'detail',
}

const { Text } = Typography
registerComponent('OrgSelector', OrgSelector)
registerComponent('UserSelector', UserSelector)
type EllipsisTextProps = {
  value?: any
  format?: string
  options?: any[]
  ellipsis?: boolean
  [key: string]: any
}
const EllipsisText = (props: EllipsisTextProps) => {
  const ellipsis = isNil(props?.ellipsis) ? true : props.ellipsis
  let text = props.value
  if (props?.format && text) {
    text = moment(props.value).format(props?.format)
  } else if (props?.options && props.options.length > 0) {
    text = map(
      filter(props?.options, (item) => item.value === props.value),
      (item) => item.label,
    ).join('；')
  } else if (isObject(text) && 'label' in text) {
    text = text.label
  }
  return <Text ellipsis={ellipsis ? { tooltip: text || '--' } : false}>{text || '--'}</Text>
}
const textField = {
  mode: 'detail',
  renderItem: () => (props) => {
    return <EllipsisText {...props} />
  },
}

type SchemaConfigFunc = (type: SchemaConfigType, extra?: Record<string, any>) => SchemaType
export const getSchema: SchemaConfigFunc = (
  type: SchemaConfigType,
  extra?: Record<string, any>,
) => {
  const schema: SchemaType = {
    userName: {
      component: 'Input',
      label: '姓名',
      ...textField,
    },
    departmentFullName: {
      component: 'Input',
      label: '部门',
      ...textField,
    },
    firstLevelDepartment: {
      component: 'Input',
      label: '当时一级部门',
      ...textField,
    },
    secondLevelDepartment: {
      component: 'Input',
      label: '当时二级部门',
      ...textField,
    },
    thirdLevelDepartment: {
      component: 'Input',
      label: '当时三级部门',
      ...textField,
    },
    fourthLevelDepartment: {
      component: 'Input',
      label: '当时四级部门',
      ...textField,
    },
    positionName: {
      component: 'Input',
      label: '当时岗位名称',
      ...textField,
    },
    empFormName: {
      component: 'Input',
      label: '当时用工形式',
      ...textField,
    },
    empCategoryName: {
      component: 'Input',
      label: '当时人员类别',
      ...textField,
    },
    contractTypeName: {
      component: 'Input',
      label: '当时合同类型',
      ...textField,
    },
    userNameUpdate: {
      component: 'Input',
      label: '最后操作人',
      ...textField,
    },
    timeUpdate: {
      component: 'DatePicker',
      label: '最后操作时间',
      props: {
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
      ...textField,
    },
    oaNo: {
      component: 'Input',
      label: '用工争议提报编号',
      ...textField,
    },
    disputeId: {
      component: 'Input',
      label: '用工争议ID',
      ...textField,
    },
    arbitrationSubject: {
      component: 'DictSelect',
      label: '起诉/仲裁主体',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.SUBJECT_OF_ARBITRATION) {
            return v[0].SUBJECT_OF_ARBITRATION.list
          }
          return []
        },
      }),
      required: true,
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    subjectName: {
      component: 'DictSelect',
      label: '主体名称',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.BM_HTZT) {
            return v[0].BM_HTZT.list
          }
          return []
        },
      }),
      required: true,
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    timeEvent: {
      component: 'DatePicker',
      label: '事件发生日期',
      required: true,
      props: {
        format: 'YYYY-MM-DD',
      },
    },
    timeIncidentYearMonth: {
      component: 'Input',
      label: '案件发生年月',
      props: {
        format: 'YYYYMM',
      },
      ...textField,
      observer: Observer({
        watch: 'timeEvent',
        action: (v) => {
          return {
            value: v ? v.format('YYYYMM') : undefined,
          }
        },
      }),
    },
    eventLocationId: {
      component: 'DictSelect',
      label: '事件发生地',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.BM_73) {
            return v[0].BM_73.list
          }
          return []
        },
      }),
      required: true,
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    isDepartmentDuty: {
      component: 'Select',
      label: '是否为部门责任',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      required: true,
    },
    deptResponsibleSituation: {
      component: 'Input.TextArea',
      label: '部门责任追责情况',
      props: {
        maxLength: 1000,
        autoSize: { minRows: 3, maxRows: 6 },
      },
    },
    eventTypeLevelFirstId: {
      component: 'DictSelect',
      label: '事件类型一级',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.BM_event_type) {
            return v[0].BM_event_type.tree
          }
          return []
        },
      }),
      required: true,
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    eventTypeLevelSecondId: {
      component: 'DictSelect',
      label: '事件类型二级',
      options: Observer({
        watch: ['eventTypeLevelFirstId', '#ehrDict'],
        action: (v, { form }) => {
          const curValue = form.getFieldValue('eventTypeLevelSecondId')
          let arr = []
          if (v && v.length > 1 && v[0] && v[1]?.BM_event_type) {
            const obj = filter(v[1]?.BM_event_type.tree, (item) => item.value === v[0])?.[0]
            arr = obj && obj.children ? obj.children : []
            if (curValue && !curValue.startsWith(v[0])) {
              form.setFieldsValue({ eventTypeLevelSecondId: undefined })
            }
          }
          return arr
        },
      }),
      required: true,
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    eventContent: {
      component: 'Input.TextArea',
      label: '案件内容',
      required: true,
      props: {
        maxLength: 1000,
        autoSize: { minRows: 3, maxRows: 6 },
      },
    },
    isSafetyEventUpgrade: {
      component: 'Select',
      label: '由人员安全事件升级',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      required: true,
    },
    crisisReportRelation: {
      component: 'Input',
      label: '关联危机管理报告单',
      ...textField,
    },
    isSpecial: {
      component: 'Select',
      label: '是否特殊提报',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      required: true,
    },
    otherEventTypeDesc: {
      component: 'Input',
      label: '其他事件类型描述',
      ...textField,
    },
    remark: {
      component: 'Input.TextArea',
      label: '备注',
      props: {
        maxLength: 1000,
        autoSize: { minRows: 3, maxRows: 6 },
      },
    },
    sscHandler: {
      component: 'UserSelector',
      label: 'SSC跟进人',
      required: true,
      props: {
        showSearch: true,
        onlySearch: true,
      },
    },
    acceptChannelId: {
      component: 'DictSelect',
      label: '受理渠道',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.BM_institution) {
            return v[0].BM_institution.list
          }
          return []
        },
      }),
      required: true,
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    currentStageProcessId: {
      component: 'DictSelect',
      label: '现阶段进程',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.BM_solution) {
            return v[0].BM_solution.list
          }
          return []
        },
      }),
      required: true,
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    timeEventClosure: {
      component: 'DatePicker',
      label: '结案日期',
      props: {
        format: 'YYYY-MM-DD',
      },
    },
    progressDetails: {
      component: 'Input.TextArea',
      label: '进度详情',
      props: {
        maxLength: 1000,
        autoSize: { minRows: 3, maxRows: 6 },
      },
    },
    eventResultId: {
      component: 'DictSelect',
      label: '结果',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.BM_result) {
            return v[0].BM_result.list
          }
          return []
        },
      }),
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    eventNum: {
      component: 'Input',
      label: '案件号',
      props: {
        maxLength: 30,
      },
    },
    eventResultDetails: {
      component: 'Input',
      label: '结果详情',
      props: {
        maxLength: 100,
      },
    },
    dataSource: {
      component: 'Input',
      label: '数据来源',
      ...textField,
    },
    terminationTypeId: {
      component: 'DictSelect',
      label: '解除用工关系类型',
      ...textField,
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.BM_Dischargetype) {
            return v[0].BM_Dischargetype.list
          }
          return []
        },
      }),
    },
    isEconCompInvolvement: {
      component: 'Select',
      label: '是否涉及经济补偿金',
      ...textField,
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    empClaimAmount: {
      component: 'InputNumber',
      label: '员工诉求金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    eventClosureAmount: {
      component: 'InputNumber',
      label: '结案金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    companyBearingAmount: {
      component: 'InputNumber',
      label: '公司承担金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    empBearingAmount: {
      component: 'InputNumber',
      label: '员工承担金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    commercialInsuranceAmount: {
      component: 'InputNumber',
      label: '商业保险承担金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    statutoryStandardAmount: {
      component: 'InputNumber',
      label: '法定标准金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    eventCategorySalaryId: {
      component: 'DictSelect',
      label: '事件类别（薪酬）',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.BM_labor_type_pay) {
            return v[0].BM_labor_type_pay.list
          }
          return []
        },
      }),
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    isRequiredPayment: {
      component: 'Select',
      label: '是否需要发放',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    payAmount: {
      component: 'InputNumber',
      label: '需付款金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    timeScheduledPayment: {
      component: 'DatePicker',
      label: '约定发放日期',
      props: {
        format: 'YYYY-MM-DD',
      },
    },
    timeActualPayment: {
      component: 'DatePicker',
      label: '实际发放日期',
      props: {
        format: 'YYYY-MM-DD',
      },
    },
    paymentResult: {
      component: 'DictSelect',
      label: '付款结果',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.BM_pay_state) {
            return v[0].BM_pay_state.list
          }
          return []
        },
      }),
    },
    paymentEntity: {
      component: 'DictSelect',
      label: '发薪主体',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v[0]?.BM_HTZT) {
            return v[0].BM_HTZT.list
          }
          return []
        },
      }),
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    payeeName: {
      component: 'Input',
      label: '收款人姓名',
      props: {
        maxLength: 30,
      },
    },
    payeeBank: {
      component: 'Input',
      label: '收款人开户银行',
      props: {
        maxLength: 30,
      },
    },
    payeeBankAccount: {
      component: 'Input',
      label: '收款人银行账号',
      props: {
        maxLength: 30,
      },
    },
    payeeInterBankNo: {
      component: 'Input',
      label: '收款人联行号',
      props: {
        maxLength: 30,
      },
    },
    attachment: {
      component: 'Upload',
      label: '',
      visible: true,
      props: {
        oss: OSS_CONFIG,
        clientCode: 'GLOBAL',
        listType: 'text',
        accept: 'image/jpg,image/jpeg,image/png,application/pdf',
        fileSizeLimit: 50,
        remark: '支持扩展名：pdf、jpg、jpeg、png，单个文件不超过 50M',
      },
    },
  }
  if (type === SchemaConfigType.Edit) {
    // 字段权限处理
    const authFields = map(
      extra?.ucDict?.[UC_DICT_KEY.EMPLOYMENT_DISPUTE_AUTH_FIELD] || [],
      (subItem) => subItem.code,
    )
    // 人员组织选择器 展示组织树结构权限
    const hasDeptAuth =
      extra?.ucDict &&
      extra?.ucDict?.[UC_DICT_KEY.DEPT_AUTH].length > 0 &&
      findIndex(extra?.ucDict?.[UC_DICT_KEY.DEPT_AUTH], (item: any) => item?.code === '1') >= 0
    map(keys(schema), (key: string) => {
      if (!authFields.includes(key) && !schema[key].mode) {
        // 是否特殊提报字段需特殊处理
        if (key === 'isSpecial') {
          schema[key].required = false
          schema[key].visible = false
        } else {
          schema[key].mode = 'detail'
          schema[key].required = false
          if (key !== 'attachment') {
            schema[key].renderItem = () => (props) => {
              return <EllipsisText {...props} />
            }
          }
        }
      }
      // 人员组织选择器控制权限
      if (['sscHandler'].includes(key)) {
        schema[key].props = {
          ...schema[key].props,
          onlySearch: !hasDeptAuth,
        }
      }
    })
  } else if (type === SchemaConfigType.Detail) {
    map(keys(schema), (key) => {
      schema[key].mode = 'detail'
      schema[key].required = false
      schema[key].renderItem = () => (props) => {
        let ellipsis = ![
          'deptResponsibleSituation',
          'eventContent',
          'remark',
          'progressDetails',
        ].includes(key)
        if (isMobile()) {
          ellipsis = false
        }
        return <EllipsisText {...props} ellipsis={ellipsis} />
      }
    })
  }
  return schema
}
