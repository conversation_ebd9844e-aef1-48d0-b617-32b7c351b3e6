import { uuid } from '@galaxy/utils'
import { cloneDeep, map, pick } from 'lodash-es'
import moment from 'moment'

// 表单项class前缀
export const FORM_ITEM_CLASS_PREFIX = `form-item-dispute_${uuid()}__`

/** 生成form item class */
export const generateFormItemClass = (name: string) => `${FORM_ITEM_CLASS_PREFIX}${name}`

/** 生成item配置 */
export const generateFormItemConfig = (name: string) => ({
  className: generateFormItemClass(name),
  field: name,
})

/** 滚动到第一个校验异常字段 */
export const scrollToFirstErrorField = (form, errorInfo) => {
  // 获取校验错误的字段名
  const errorFields = errorInfo?.errorFields || []
  // 如果有错误字段，可以滚动到第一个错误字段的位置
  if (errorFields.length > 0) {
    const firstErrorField = errorFields[0].name[0]
    const node = firstErrorField
      ? document.getElementsByClassName(generateFormItemClass(firstErrorField))
      : null
    if (node && node.length > 0) {
      node[0].scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }
}

// 将表单数据转换生成提交数据
export const transformSubmitData = (values: Record<string, any>) => {
  const newValues = cloneDeep(values)
  map(values, (v: any, key: string) => {
    switch (key) {
      case 'attachment':
        newValues[key] =
          v && v.length > 0
            ? JSON.stringify(map(v, (item) => pick(item, ['uuid', 'name'])))
            : undefined
        break
      case 'sscHandler':
        newValues[key] = v?.value
        break
      case 'timeEvent':
      case 'timeScheduledPayment':
      case 'timeActualPayment':
      case 'timeEventClosure':
        newValues[key] = v ? v.endOf('day').valueOf() : undefined
        break
      default:
    }
  })
  return newValues
}

// 将服务端数据转成表单数据
export const transformDataToFormData = (values?: any) => {
  if (!values) {
    return values
  }
  const newValues = cloneDeep(values) as any
  map(values, (v: any, key: string) => {
    switch (key) {
      case 'attachment':
        newValues[key] = v ? JSON.parse(v) : undefined
        break
      case 'sscHandler':
        newValues[key] = v ? { label: values?.sscHandlerName, value: v } : undefined
        break
      case 'timeEvent':
      case 'timeScheduledPayment':
      case 'timeActualPayment':
      case 'timeEventClosure':
        newValues[key] = v ? moment(v) : undefined
        break
      case 'userNameUpdate':
        newValues[key] = values.userNameUpdate
          ? `${values.userNameUpdate}(${values.userIdUpdate})`
          : undefined
        break
      default:
        newValues[key] = v === '' ? undefined : v
    }
    if (v === '' || v === null) {
      newValues[key] = undefined
    }
  })
  return newValues
}
