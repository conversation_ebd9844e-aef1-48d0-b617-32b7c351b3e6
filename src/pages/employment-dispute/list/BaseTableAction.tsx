import React from 'react'
import TablePermissionAction, {
  PermissionActionItem,
} from '@/components/businessPermission/TablePermissionAction'
import { useNavigate } from 'react-router-dom'
import { message } from 'antd'
import { confirmDialog } from '@/components/Dialog'
import { PAYMENT_STATUS } from '@/constants'
import { addOaDisputePayment } from '@/api/employment-dispute'
import { EMPLOYMENT_DISPUTE } from '@/constants/rbac-code/employment-dispute'

type IProps = {
  reloadList: () => void
  expandNumber?: number
  record: any
}

export enum OperatorType {
  detail = 'detail', // 查看
  oaPay = 'oaPay', // 发起付款
}

const BaseTableActions = (props: IProps) => {
  const { expandNumber, record, reloadList } = props
  const navigate = useNavigate()

  const oaPay = () => {
    confirmDialog({
      title: '确定要发起付款吗？',
      content: '确定后，会向OA发起付款确认流程',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const res = await addOaDisputePayment({ id: record.id })
        if (res.data) {
          reloadList?.()
          message.success('发起付款成功')
        } else {
          message.error('发起付款失败')
        }
      },
    })
  }

  const operatorClick = (key) => {
    switch (key) {
      case OperatorType.detail:
        navigate(`/employment-dispute/detail?id=${record.id}`)
        break
      case OperatorType.oaPay:
        oaPay()
        break
      default:
    }
  }

  const getOperatorColumns = () => {
    const actions: PermissionActionItem[] = [
      {
        key: OperatorType.detail,
        label: '查看',
        order: 1,
        permissionCode: EMPLOYMENT_DISPUTE.DETAIL,
      },
      {
        key: OperatorType.oaPay,
        label: '发起付款',
        order: 2,
        permissionCode: EMPLOYMENT_DISPUTE.DISPUTE_PAYMENT,
        disabled: [PAYMENT_STATUS.approved, PAYMENT_STATUS.inReview].includes(record.paymentStatus),
      },
    ]

    return actions
  }

  return (
    <TablePermissionAction
      expandNumber={expandNumber}
      actions={getOperatorColumns()}
      menuClick={operatorClick}
    />
  )
}

export default BaseTableActions
