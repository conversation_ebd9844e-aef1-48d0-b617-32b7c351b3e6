import React from 'react'
import { Observer, registerComponent } from '@amazebird/antd-schema-form'
import { TreeSelect as OrgSelector } from '@galaxy/org-selector'
import moment from 'moment'
import { TABLE_TOOLTIP_ELLIPSIS, UC_DICT_KEY } from '@/constants'
import { findIndex } from 'lodash-es'
import { Tooltip } from 'antd'
import BaseTableAction from './BaseTableAction'

registerComponent('OrgSelector', OrgSelector)
// 少了区域、案发合同主体、发起付款状态、未操作者
export const getColumns = ({ ehrDict, dhrDict, reloadList }) => [
  {
    cell: 'Text',
    // cell: { props: { ellipsis: { direction: 'end' } }, type: 'Text' },
    dataIndex: 'id',
    title: '事件ID',
    width: 80,
    fixed: 'left',
  },
  {
    cell: 'Text',
    options: [],
    dataIndex: 'userName',
    title: '姓名',
    width: 160,
    fixed: 'left',
    render: (_, record) => {
      return `${record.userName}（${record.userNum}）`
    },
  },
  {
    cell: 'Text',
    // cell: { props: { ellipsis: { direction: 'end' } }, type: 'Text' },
    options: [],
    dataIndex: 'departmentFullName',
    title: '所属部门',
    width: 150,
    render: (_, record) => {
      if (record.departmentFullName) {
        const lastDeptName = record.departmentFullName.split('\\').pop()
        return <Tooltip title={record.departmentFullName}>{lastDeptName}</Tooltip>
      }
      return '--'
    },
  },
  {
    cell: 'Text',
    options: [],
    dataIndex: 'positionName',
    title: '当时岗位名称',
    width: 150,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_KK?.list,
    dataIndex: 'empCategoryId',
    title: '当时人员类别',
    width: 150,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_VAA?.list,
    dataIndex: 'contractTypeId',
    title: '当时合同类型',
    width: 150,
  },
  {
    // cell: 'Text',
    cell: { props: { ellipsis: { direction: 'end' } }, type: 'Text' },
    options: dhrDict?.SUBJECT_OF_ARBITRATION?.list,
    dataIndex: 'arbitrationSubject',
    title: '起诉/仲裁主体',
    width: 180,
  },
  {
    cell: { props: { format: 'YYYYMM' }, type: 'DateTime' },
    dataIndex: 'timeIncidentYearMonth',
    title: '案发年月',
    width: 100,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_BZGSQY?.list,
    dataIndex: 'accidentArea',
    title: '所属区域',
    width: 150,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_event_type?.list,
    dataIndex: 'eventTypeLevelFirstId',
    title: '事件类型一级',
    width: 150,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_event_type?.list,
    dataIndex: 'eventTypeLevelSecondId',
    title: '事件类型二级',
    width: 180,
  },
  {
    // cell: 'Text',
    cell: {
      props: {
        ellipsis: {
          direction: 'end',
          ...TABLE_TOOLTIP_ELLIPSIS,
        },
      },
      type: 'Text',
    },
    dataIndex: 'eventContent',
    title: '案件内容',
    width: 400,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_institution?.list,
    dataIndex: 'acceptChannelId',
    title: '受理渠道',
    width: 150,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_solution?.list,
    dataIndex: 'currentStageProcessId',
    title: '现阶段进程',
    width: 120,
  },
  {
    // cell: 'Text',
    cell: { props: { ellipsis: { direction: 'end' } }, type: 'Text' },
    options: ehrDict?.BM_HTZT?.list,
    dataIndex: 'subjectName',
    title: '案发合同主体',
    width: 180,
  },
  {
    cell: 'Text',
    options: dhrDict?.PAYMENT_STATUS?.list,
    dataIndex: 'paymentStatus',
    title: '发起付款状态',
    width: 120,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_pay_state?.list,
    dataIndex: 'paymentResult',
    title: '付款结果',
    width: 100,
  },
  {
    key: '_operator',
    title: '操作',
    fixed: 'right',
    width: 120,
    render: (_, record) => <BaseTableAction record={record} reloadList={reloadList} />,
  },
]

export const getSearchColumns: any = ({ ucDict }) => [
  {
    title: '所属区域',
    component: 'DictSelect',
    options: Observer({
      watch: ['#ehrDict'],
      action: (v) => {
        if (v && v.length > 0 && v?.[0]) {
          return v?.[0].BM_BZGSQY?.list || []
        }
        return []
      },
    }),
    props: { labelInValue: true, mode: 'multiple', maxTagCount: 'responsive' },
    dataIndex: 'accidentAreas',
    key: 'accidentAreas',
  },
  {
    title: '姓名',
    component: 'Input',
    dataIndex: 'userNum',
    key: 'userNum',
  },
  {
    props: {
      format: 'YYYY-MM',
      picker: 'month',
      disabledDate: (current) => {
        const currentYearMonth = moment().endOf('year').format('YYYY-MM')
        const yearMonth = current.format('YYYY-MM')
        return yearMonth > currentYearMonth
      },
    },
    title: '案发时间',
    component: 'RangePicker',
    dataIndex: 'timeAccident',
    key: 'timeAccident',
  },
  {
    title: '案发合同主体',
    component: 'Select',
    options: Observer({
      watch: ['#contractSubject'],
      action: (v) => {
        return v?.[0] || []
      },
    }),
    props: {
      labelInValue: true,
      mode: 'multiple',
      maxTagCount: 'responsive',
      showSearch: true,
      optionFilterProp: 'label',
    },
    dataIndex: 'accidentContractSubjects',
    key: 'accidentContractSubjects',
  },
  {
    title: '所属部门',
    placeholder: '请选择所属部门',
    component: 'OrgSelector',
    dataIndex: 'departmentId',
    key: 'departmentId',
    props: {
      onlySearch: !(
        ucDict &&
        ucDict?.[UC_DICT_KEY.DEPT_AUTH].length > 0 &&
        findIndex(ucDict?.[UC_DICT_KEY.DEPT_AUTH], (item: any) => item?.code === '1') >= 0
      ),
      showSearch: true,
    },
  },
  {
    title: '付款结果',
    component: 'DictSelect',
    options: Observer({
      watch: ['#ehrDict'],
      action: (v) => {
        if (v && v.length > 0 && v?.[0]) {
          return v?.[0].BM_pay_state?.list || []
        }
        return []
      },
    }),
    props: { labelInValue: true, showSearch: true, optionFilterProp: 'label' },
    dataIndex: 'paymentResult',
    key: 'paymentResult',
  },
  {
    title: '现阶段进程',
    component: 'DictSelect',
    options: Observer({
      watch: ['#ehrDict'],
      action: (v) => {
        if (v && v.length > 0 && v?.[0]) {
          return v?.[0].BM_solution?.list || []
        }
        return []
      },
    }),
    props: { labelInValue: true, showSearch: true, optionFilterProp: 'label' },
    dataIndex: 'process',
    key: 'process',
  },
]
