import React from 'react'
import TablePermissionAction, {
  PermissionActionItem,
} from '@/components/businessPermission/TablePermissionAction'
import { SECURITY } from '@/constants/rbac-code/security'
import { useSecurityOperations, OperatorType } from './hooks/useSecurityOperations'

type IProps = {
  expandNumber?: number
  record: any
  dict: any
}

const TableActions = (props: IProps) => {
  const { expandNumber, record, dict } = props
  const { operatorClick } = useSecurityOperations({ record, dict })

  const getOperatorColumns = () => {
    const actions: PermissionActionItem[] = [
      {
        key: OperatorType.detail,
        label: '查看',
        order: 1,
        permissionCode: SECURITY.DETAIL,
      },
      {
        key: OperatorType.compensationApply,
        label: '补偿额度申请',
        order: 2,
        permissionCode: SECURITY.DETAIL,
      },
      // {
      //   key: OperatorType.paymentApply,
      //   label: '安全事件付款申请',
      //   order: 3,
      //   permissionCode: SECURITY.DETAIL,
      // },
      // {
      //   key: OperatorType.loanApply,
      //   label: '安全事件借款申请',
      //   order: 4,
      //   permissionCode: SECURITY.DETAIL,
      // },
      {
        key: OperatorType.repaymentApproval,
        label: '回款审批',
        order: 5,
        permissionCode: SECURITY.DETAIL,
      },
      {
        key: OperatorType.noRepaymentApply,
        label: '不追偿申请',
        order: 6,
        permissionCode: SECURITY.DETAIL,
      },
      {
        key: OperatorType.upgradeToDispute,
        label: '升级至安全争议',
        order: 6,
        permissionCode: SECURITY.DETAIL,
      },
      {
        key: OperatorType.close,
        label: '结案',
        order: 7,
        permissionCode: SECURITY.DETAIL,
      },
      {
        key: OperatorType.withdraw,
        label: '撤案',
        order: 8,
        permissionCode: SECURITY.DETAIL,
      },
      {
        key: OperatorType.cancelWithdraw,
        label: '取消撤案',
        order: 9,
        permissionCode: SECURITY.DETAIL,
      },
    ]

    return actions
  }

  return (
    <TablePermissionAction
      expandNumber={expandNumber}
      actions={getOperatorColumns()}
      menuClick={operatorClick}
    />
  )
}

export default TableActions
