import {
  TABLE_TOOLTIP_ELLIPSIS,
  UC_DICT_KEY,
  SECURITY_EVENT_PROCESS,
  SECURITY_EVENT_PROCESS_COLOR,
  YES_NO_OPTIONS,
} from '@/constants'
import { Observer, registerComponent } from '@amazebird/antd-schema-form'
import { TreeSelect as OrgSelector } from '@galaxy/org-selector'
import { findIndex } from 'lodash-es'
import React from 'react'
import { Tooltip } from 'antd'
import { getcontractSubject } from '@/api/securityEventAdminController'
import moment from 'moment'
import TableActions from './TableAction'

registerComponent('OrgSelector', OrgSelector)

export const getColumns = ({ ehrDict }) => [
  {
    dataIndex: 'id',
    title: '事件ID',
    width: 150,
    fixed: 'left',
  },
  {
    cell: {
      type: 'Text',
      render: ({ _, record }) => `${record.userName}(${record.userNum})`,
    },
    options: [],
    dataIndex: 'userName',
    title: '姓名',
    width: 150,
    fixed: 'left',
  },
  {
    cell: 'Text',
    options: [],
    dataIndex: 'departmentFullName',
    title: '所属部门',
    width: 150,
    render: (_, record) => {
      if (record.departmentFullName) {
        const lastIndex = record.departmentFullName.lastIndexOf('\\')
        const result = record.departmentFullName.substring(lastIndex + 1)
        return (
          <Tooltip title={record.departmentFullName}>
            <div>{result}</div>
          </Tooltip>
        )
      }
      return '--'
    },
  },
  {
    cell: 'Text',
    options: [],
    dataIndex: 'accidentPositionName',
    title: '当时岗位名称',
    width: 150,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_KK?.list,
    dataIndex: 'employmentState',
    title: '当时人员类别',
    width: 150,
  },
  // {
  //   cell: { props: { ellipsis: { direction: 'end' } }, type: 'Text' },
  //   options: ehrDict?.BM_HTZT?.list,
  //   dataIndex: 'accidentContractSubject',
  //   title: '案发合同主体',
  //   width: 180,
  // },
  {
    cell: { props: { ellipsis: { direction: 'end' } }, type: 'Text' },
    dataIndex: 'accidentContractSubjectName',
    title: '案发合同主体',
    width: 180,
  },
  {
    cell: { props: { format: 'YYYYMM' }, type: 'DateTime' },
    dataIndex: 'accidentYearMonth',
    title: '案发年月',
    width: 100,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_BZGSQY?.list,
    dataIndex: 'accidentArea',
    title: '所属区域',
    width: 150,
  },
  {
    cell: 'Text',
    options: ehrDict?.BM_AJXS?.list,
    dataIndex: 'eventCoefficient',
    title: '案件系数',
    width: 150,
  },
  {
    cell: 'Status',
    options: ehrDict?.BX_AJJD?.list.map((item) => {
      return {
        ...item,
        color: SECURITY_EVENT_PROCESS_COLOR[item.value],
      }
    }),
    dataIndex: 'process',
    title: '进度',
    width: 150,
  },
  {
    title: '跟进状态',
    cell: 'Text',
    dataIndex: 'followStatus',
    width: 150,
    render: (text, record) => {
      if (record.process !== SECURITY_EVENT_PROCESS.inProgress) {
        return '--'
      }
      return text || '--'
    },
  },
  {
    title: '是否争议',
    dataIndex: 'isDispute',
    width: 150,
    render: (isDispute) => {
      return isDispute ? '是' : '否'
    },
  },
  {
    cell: {
      type: 'Text',
      props: {
        ellipsis: { direction: 'end', ...TABLE_TOOLTIP_ELLIPSIS },
      },
    },
    options: [],
    dataIndex: 'processRemark',
    title: '进度说明',
    width: 200,
  },
  {
    cell: {
      type: 'Text',
      props: {
        ellipsis: { direction: 'end', ...TABLE_TOOLTIP_ELLIPSIS },
      },
    },
    options: [],
    dataIndex: 'accidentContent',
    title: '案件内容',
    width: 350,
  },
  {
    key: '_operator',
    title: '操作',
    fixed: 'right',
    width: 200,
    cell: {
      type: 'Operator',
      props: {
        size: 3,
      },
    },
    render: (_, record) => <TableActions record={record} dict={ehrDict} />,
  },
]

export const getSearchColumns = ({ ucDict }) => [
  {
    dataIndex: 'accidentAreas',
    title: '所属区域',
    component: 'DictSelect',
    options: Observer({
      watch: ['#ehrDict'],
      action: (v) => {
        if (v && v.length > 0 && v?.[0]) {
          return v?.[0].BM_BZGSQY?.list || []
        }
        return []
      },
    }),
    props: { labelInValue: true, mode: 'multiple', maxTagCount: 'responsive' },
    key: 'accidentAreas',
  },
  {
    props: { defaultLevel: 2 },
    dataIndex: 'userNum',
    title: '姓名',
    component: 'Input',
    placeholder: '请输入姓名或工号',
    key: 'userNum',
  },
  {
    dataIndex: 'timeAccident',
    title: '案发时间',
    component: 'RangePicker',
    key: 'timeAccident',
    props: {
      format: 'YYYY-MM',
      picker: 'month',
      disabledDate: (current) => {
        const currentYearMonth = moment().endOf('year').format('YYYY-MM')
        const yearMonth = current.format('YYYY-MM')
        return yearMonth > currentYearMonth
      },
    },
  },
  {
    key: 'accidentContractSubjects',
    dataIndex: 'accidentContractSubjects',
    title: '案发合同主体',
    component: 'DictSelect',
    options: Observer({
      watch: ['#ehrDict'],
      action: async (v) => {
        const { data } = await getcontractSubject()
        if (v && v.length > 0 && v?.[0]) {
          return (
            v?.[0].BM_HTZT?.list.filter((item) =>
              data?.some((value) => value.toString() === item.value),
            ) || []
          )
        }
        return []
      },
    }),
    props: {
      labelInValue: true,
      mode: 'multiple',
      maxTagCount: 'responsive',
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  {
    dataIndex: 'departmentId',
    title: '所属部门',
    component: 'OrgSelector',
    placeholder: '请选择所属部门',
    key: 'departmentId',
    props: {
      onlySearch: !(
        ucDict &&
        ucDict?.[UC_DICT_KEY.DEPT_AUTH].length > 0 &&
        findIndex(ucDict?.[UC_DICT_KEY.DEPT_AUTH], (item: any) => item?.code === '1') >= 0
      ),
      showSearch: true,
    },
  },
  {
    dataIndex: 'process',
    title: '进度',
    component: 'DictSelect',
    options: Observer({
      watch: ['#ehrDict'],
      action: (v) => {
        if (v && v.length > 0 && v?.[0]) {
          return v?.[0].BX_AJJD?.list || []
        }
        return []
      },
    }),
    props: { labelInValue: true },
    key: 'process',
  },
  {
    title: '是否争议',
    dataIndex: 'isDispute',
    key: 'isDispute',
    component: 'Select',
    options: YES_NO_OPTIONS,
  },
]
