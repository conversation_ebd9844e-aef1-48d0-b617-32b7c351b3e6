import React, { useMemo } from 'react'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import { Card, Row, Col } from 'antd'
import { map } from 'lodash-es'
import { isMobile } from '@galaxy/utils'
import { createFormItemConfig } from '../../utils'
import style from '../index.module.less'
import { getSchema } from '../schema'
import '@amazebird/antd-business-field'

type IProps = {
  form?: any
  [key: string]: any
}

export default function DetailForm(props: IProps) {
  const { ...rest } = props
  const schema = useMemo(() => getSchema(), [])
  const generateFormItemConfig = useMemo(() => createFormItemConfig('security-compensation'), [])

  const basicProps = {
    labelCol: { flex: '135px' },
    ...(isMobile() ? { wrapperCol: { flex: '1' } } : { wrapperCol: { span: 17 } }),
  }

  const renderItem = (key: string) => {
    return (
      <Col xs={24} md={12} key={key}>
        <Item {...generateFormItemConfig(key)} {...basicProps} />
      </Col>
    )
  }

  return (
    <SchemaForm
      {...rest}
      schema={schema}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 10 }}
      className={style.compensationForm}
    >
      <Card title="基本信息" size="default">
        <Row gutter={0} align="top">
          {map(
            [
              'id',
              'department',
              'user',
              'type',
              'responsibilityAllocation',
              'compensationType',
              'accidentNature',
            ],
            (key) => renderItem(key),
          )}
        </Row>
      </Card>

      <Card title="法定赔偿项目" size="default">
        <Row gutter={0} align="top">
          {map(
            [
              'medicalExpenses',
              'workRehabilitationWages',
              'oneTimeDisabilityAllowance',
              'oneTimeWorkInjuryMedicalAllowance',
              'oneTimeDisabilityEmploymentAllowance',
              'employeeTransportationFee',
              'employeeHospitalizationMealAllowance',
              'employeeDisabilityAids',
              'employeeOtherExpenses',
              'thirdPartyMedicalExpenses',
              'followUpTreatmentFee',
              'thirdPartyHospitalizationMealAllowance',
              'inpatientNursingFee',
              'outpatientNursingFee',
              'lostWorkFee',
              'nutritionFee',
              'thirdPartyTransportationFee',
              'disabilityCompensation',
              'thirdPartyDisabilityAids',
              'deathCompensation',
              'funeralExpenses',
              'mentalDistressCompensation',
              'dependentLivingExpenses',
              'appraisalFee',
              'directPropertyLoss',
              'indirectPropertyLoss',
              'thirdPartyOtherExpenses',
              'medicalExpensesTotal',
              'legalCompensationAmount',
            ],
            (key) => renderItem(key),
          )}
        </Row>
      </Card>

      <Card title="调解费用" size="default">
        <Row gutter={0} align="top">
          {map(
            [
              'caseApplicationMediationAmount',
              'insuranceEstimatedAmount',
              'thirdPartyLiabilityAmount',
              'workInjuryInsuranceClaimAmount',
              'employeeEstimatedAmount',
              'companyEstimatedAmount',
              'employeeBorrowAmount',
              'companyAdvanceAmount',
            ],
            (key) => renderItem(key),
          )}
        </Row>
        <Item {...generateFormItemConfig('compensationDescription')} {...basicProps} />
      </Card>

      <Card title="附件" size="default">
        <Item {...generateFormItemConfig('attachment')} {...basicProps} />
      </Card>
    </SchemaForm>
  )
}
