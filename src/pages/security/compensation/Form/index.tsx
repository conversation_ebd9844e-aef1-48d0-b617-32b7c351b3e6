import React from 'react'
import { renderStateCenterCtx, StateCenter } from '@amazebird/utils'
import EditForm from './EditForm'
import DetailForm from './DetailForm'

type IProps = {
  form?: any
  batchPackDownload?: () => void
  [key: string]: any
}

const bindData = [
  {
    name: 'CompensationForm',
    fields: [
      'id',
      'department',
      'user',
      'type',
      'responsibilityAllocation',
      'compensationType',
      'accidentNature',
      'medicalExpenses',
      'workRehabilitationWages',
      'oneTimeDisabilityAllowance',
      'oneTimeWorkInjuryMedicalAllowance',
      'oneTimeDisabilityEmploymentAllowance',
      'employeeTransportationFee',
      'employeeHospitalizationMealAllowance',
      'employeeDisabilityAids',
      'employeeOtherExpenses',
      'thirdPartyMedicalExpenses',
      'followUpTreatmentFee',
      'thirdPartyHospitalizationMealAllowance',
      'inpatientNursingFee',
      'outpatientNursingFee',
      'lostWorkFee',
      'nutritionFee',
      'thirdPartyTransportationFee',
      'disabilityCompensation',
      'thirdPartyDisabilityAids',
      'deathCompensation',
      'funeralExpenses',
      'mentalDistressCompensation',
      'dependentLivingExpenses',
      'appraisalFee',
      'directPropertyLoss',
      'indirectPropertyLoss',
      'thirdPartyOtherExpenses',
      'medicalExpensesTotal',
      'legalCompensationAmount',
      'caseApplicationMediationAmount',
      'insuranceEstimatedAmount',
      'thirdPartyLiabilityAmount',
      'workInjuryInsuranceClaimAmount',
      'employeeEstimatedAmount',
      'companyEstimatedAmount',
      'employeeBorrowAmount',
      'companyAdvanceAmount',
      'compensationDescription',
      'attachment',
    ],
  },
]

export function CompensationForm(props: IProps) {
  const initialStates = {}

  const stateCenter = new StateCenter({
    initialStates,
    bindData,
  })

  return renderStateCenterCtx(stateCenter, <EditForm {...props} />)
}

export function CompensationDetailForm(props: IProps) {
  const initialStates = {}

  const stateCenter = new StateCenter({
    initialStates,
    bindData,
  })

  return renderStateCenterCtx(stateCenter, <DetailForm {...props} />)
}
