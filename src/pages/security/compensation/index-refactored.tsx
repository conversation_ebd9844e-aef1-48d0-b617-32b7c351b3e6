import React, { useMemo, useState } from 'react'
import { <PERSON><PERSON>, Divider } from 'antd'
import { Item } from '@amazebird/antd-schema-form'
import { StateCenterDecorator, useStateWatch } from '@amazebird/utils'
import { keys } from 'lodash-es'
import useBatchPackDownload from '@/hooks/useBatchPackDownload'
import { SecurityFormWrapper, FormCard } from '../components'
import { createFormItemConfig } from '../utils'
import style from './index.module.less'
import { getSchema } from './schema'
import '@amazebird/antd-business-field'

const CompensationRefactored: React.FC = () => {
  const schema = useMemo(() => getSchema(), [])
  const generateFormItemConfig = useMemo(() => createFormItemConfig('security-compensation'), [])
  const [compensationType] = useStateWatch(['compensationType'])
  const { batchPackDownload } = useBatchPackDownload()
  const [hasAttachment, setHasAttachment] = useState(false)

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      if (changedValues[key] && changedValues[key].length > 0 && !hasAttachment) {
        setHasAttachment(true)
      } else if ((!changedValues[key] || changedValues[key].length <= 0) && hasAttachment) {
        setHasAttachment(false)
      }
    }
  }

  // 基本信息字段
  const basicInfoFields = [
    { name: 'id' },
    { name: 'department' },
    { name: 'user' },
    { name: 'type' },
    { name: 'responsibilityAllocation' },
    { name: 'compensationType' },
    { name: 'accidentNature' },
  ]

  // 员工赔偿项目字段
  const employeeCompensationFields = [
    { name: 'medicalExpenses' },
    { name: 'workRehabilitationWages' },
    { name: 'oneTimeDisabilityAllowance' },
    { name: 'oneTimeWorkInjuryMedicalAllowance' },
    { name: 'oneTimeDisabilityEmploymentAllowance' },
    { name: 'employeeTransportationFee' },
    { name: 'employeeHospitalizationMealAllowance' },
    { name: 'employeeDisabilityAids' },
    { name: 'employeeOtherExpenses' },
  ]

  // 三者赔偿项目字段
  const thirdPartyCompensationFields = [
    { name: 'thirdPartyMedicalExpenses' },
    { name: 'followUpTreatmentFee' },
    { name: 'thirdPartyHospitalizationMealAllowance' },
    { name: 'inpatientNursingFee' },
    { name: 'outpatientNursingFee' },
    { name: 'lostWorkFee' },
    { name: 'nutritionFee' },
    { name: 'thirdPartyTransportationFee' },
    { name: 'disabilityCompensation' },
    { name: 'thirdPartyDisabilityAids' },
    { name: 'deathCompensation' },
    { name: 'funeralExpenses' },
    { name: 'mentalDistressCompensation' },
    { name: 'dependentLivingExpenses' },
    { name: 'appraisalFee' },
    { name: 'directPropertyLoss' },
    { name: 'indirectPropertyLoss' },
    { name: 'thirdPartyOtherExpenses' },
  ]

  // 赔偿合计字段
  const compensationTotalFields = [
    { name: 'medicalExpensesTotal' },
    { name: 'legalCompensationAmount' },
  ]

  // 调解费用字段
  const mediationFeeFields = [
    { name: 'caseApplicationMediationAmount' },
    { name: 'insuranceEstimatedAmount' },
    { name: 'thirdPartyLiabilityAmount' },
    { name: 'workInjuryInsuranceClaimAmount' },
    { name: 'employeeEstimatedAmount' },
    { name: 'companyEstimatedAmount' },
    { name: 'employeeBorrowAmount' },
    { name: 'companyAdvanceAmount' },
  ]

  return (
    <SecurityFormWrapper
      pageName="security-compensation"
      schema={schema}
      className={style.compensationForm}
      onValuesChange={onValuesChange}
      attachmentExtra={
        <Button
          type="link"
          disabled={!hasAttachment}
          onClick={() => {
            // TODO: 获取表单实例并处理下载
            const fileName = 'compensation'
            // batchPackDownload(attachment, fileName)
          }}
        >
          打包下载
        </Button>
      }
    >
      {/* 基本信息卡片 */}
      <FormCard
        title="基本信息"
        fields={basicInfoFields}
        generateFormItemConfig={generateFormItemConfig}
      />

      {/* 法定赔偿项目卡片 */}
      <FormCard
        title="法定赔偿项目"
        fields={[]}
        generateFormItemConfig={generateFormItemConfig}
      >
        {/* 员工赔偿项目 */}
        {compensationType === '1' && (
          <div className={style.employeeCompensation}>
            <div className={style.subTitle}>员工赔偿项目</div>
            <FormCard
              title=""
              fields={employeeCompensationFields}
              generateFormItemConfig={generateFormItemConfig}
            />
            <Divider />
          </div>
        )}

        {/* 三者赔偿项目 */}
        {compensationType === '2' && (
          <div className={style.thirdPartyCompensation}>
            <div className={style.subTitle}>三者赔偿项目</div>
            <FormCard
              title=""
              fields={thirdPartyCompensationFields}
              generateFormItemConfig={generateFormItemConfig}
            />
            <Divider />
          </div>
        )}

        {/* 赔偿项目合计 */}
        <div className={style.subTitle}>赔偿项目合计</div>
        <FormCard
          title=""
          fields={compensationTotalFields}
          generateFormItemConfig={generateFormItemConfig}
        />
      </FormCard>

      {/* 调解费用卡片 */}
      <FormCard
        title="调解费用"
        fields={mediationFeeFields}
        generateFormItemConfig={generateFormItemConfig}
      >
        {/* 赔偿情况说明 */}
        <Item
          {...generateFormItemConfig('compensationDescription')}
          wrapperCol={{ span: 17 }}
          labelCol={{ span: 4 }}
        />
      </FormCard>
    </SecurityFormWrapper>
  )
}

export default StateCenterDecorator()(CompensationRefactored)
