import React, { useMemo, useState } from 'react'
import { SchemaForm, Item, FormItemGrid } from '@amazebird/antd-schema-form'
import { <PERSON><PERSON>, Card, Divider } from 'antd'
import { StateCenterDecorator, useStateWatch } from '@amazebird/utils'
// import moment from 'moment'
import useBatchPackDownload from '@/hooks/useBatchPackDownload'
import { keys } from 'lodash-es'
import { createFormItemConfig } from '../utils'
import style from './index.module.less'
import { getSchema } from './schema'
import '@amazebird/antd-business-field'

const Compensation: React.FC = () => {
  const form = SchemaForm.createForm()
  const schema = useMemo(() => getSchema(), [])
  const generateFormItemConfig = useMemo(() => createFormItemConfig('security-compensation'), [])
  const [compensationType] = useStateWatch(['compensationType'])
  const { batchPackDownload } = useBatchPackDownload()
  const [hasAttachment, setHasAttachment] = useState(false)

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      if (changedValues[key] && changedValues[key].length > 0 && !hasAttachment) {
        setHasAttachment(true)
      } else if ((!changedValues[key] || changedValues[key].length <= 0) && hasAttachment) {
        setHasAttachment(false)
      }
    }
  }

  return (
    <div>
      <SchemaForm
        schema={schema}
        layout="horizontal"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 10 }}
        form={form}
        className={style.compensationForm}
        onValuesChange={onValuesChange}
      >
        <Card title="基本信息" size="default" key="basicInfo">
          <FormItemGrid fillEmpty colCount={2}>
            <Item {...generateFormItemConfig('id')} />
            <Item {...generateFormItemConfig('department')} />
            <Item {...generateFormItemConfig('user')} />
            <Item {...generateFormItemConfig('type')} />
            <Item {...generateFormItemConfig('responsibilityAllocation')} />
            <Item {...generateFormItemConfig('compensationType')} />
            <Item {...generateFormItemConfig('accidentNature')} />
          </FormItemGrid>
        </Card>
        <Card title="法定赔偿项目" size="default" key="legalCompensation">
          {compensationType === '1' && (
            <div className={style.employeeCompensation}>
              <div className={style.subTitle}>员工赔偿项目</div>
              <FormItemGrid fillEmpty colCount={2}>
                <Item {...generateFormItemConfig('medicalExpenses')} />
                <Item {...generateFormItemConfig('workRehabilitationWages')} />
                <Item {...generateFormItemConfig('oneTimeDisabilityAllowance')} />
                <Item {...generateFormItemConfig('oneTimeWorkInjuryMedicalAllowance')} />
                <Item {...generateFormItemConfig('oneTimeDisabilityEmploymentAllowance')} />
                <Item {...generateFormItemConfig('employeeTransportationFee')} />
                <Item {...generateFormItemConfig('employeeHospitalizationMealAllowance')} />
                <Item {...generateFormItemConfig('employeeDisabilityAids')} />
                <Item {...generateFormItemConfig('employeeOtherExpenses')} />
              </FormItemGrid>
              <Divider />
            </div>
          )}
          {compensationType === '2' && (
            <div className={style.thirdPartyCompensation}>
              <div className={style.subTitle}>三者赔偿项目</div>
              <FormItemGrid fillEmpty colCount={2}>
                <Item {...generateFormItemConfig('thirdPartyMedicalExpenses')} />
                <Item {...generateFormItemConfig('followUpTreatmentFee')} />
                <Item {...generateFormItemConfig('thirdPartyHospitalizationMealAllowance')} />
                <Item {...generateFormItemConfig('inpatientNursingFee')} />
                <Item {...generateFormItemConfig('outpatientNursingFee')} />
                <Item {...generateFormItemConfig('lostWorkFee')} />
                <Item {...generateFormItemConfig('nutritionFee')} />
                <Item {...generateFormItemConfig('thirdPartyTransportationFee')} />
                <Item {...generateFormItemConfig('disabilityCompensation')} />
                <Item {...generateFormItemConfig('thirdPartyDisabilityAids')} />
                <Item {...generateFormItemConfig('deathCompensation')} />
                <Item {...generateFormItemConfig('funeralExpenses')} />
                <Item {...generateFormItemConfig('mentalDistressCompensation')} />
                <Item {...generateFormItemConfig('dependentLivingExpenses')} />
                <Item {...generateFormItemConfig('appraisalFee')} />
                <Item {...generateFormItemConfig('directPropertyLoss')} />
                <Item {...generateFormItemConfig('indirectPropertyLoss')} />
                <Item {...generateFormItemConfig('thirdPartyOtherExpenses')} />
              </FormItemGrid>
              <Divider />
            </div>
          )}
          <div className={style.subTitle}>赔偿项目合计</div>
          <FormItemGrid fillEmpty colCount={2}>
            <Item {...generateFormItemConfig('medicalExpensesTotal')} />
            <Item {...generateFormItemConfig('legalCompensationAmount')} />
          </FormItemGrid>
        </Card>
        <Card title="调解费用" size="default" key="mediationFee">
          <FormItemGrid fillEmpty colCount={2}>
            <Item {...generateFormItemConfig('caseApplicationMediationAmount')} />
            <Item {...generateFormItemConfig('insuranceEstimatedAmount')} />
            <Item {...generateFormItemConfig('thirdPartyLiabilityAmount')} />
            <Item {...generateFormItemConfig('workInjuryInsuranceClaimAmount')} />
            <Item {...generateFormItemConfig('employeeEstimatedAmount')} />
            <Item {...generateFormItemConfig('companyEstimatedAmount')} />
            <Item {...generateFormItemConfig('employeeBorrowAmount')} />
            <Item {...generateFormItemConfig('companyAdvanceAmount')} />
          </FormItemGrid>
          <Item
            {...generateFormItemConfig('compensationDescription')}
            wrapperCol={{ span: 17 }}
            labelCol={{ span: 4 }}
          />
        </Card>
        <Card
          title="附件"
          size="default"
          key="appendix"
          extra={
            // <BusinessPermission code={SECURITY.BATCH_DOWNLOAD}>
            <Button
              type="link"
              disabled={!hasAttachment}
              onClick={() => {
                const attachment = form.getFieldValue('attachment')
                // TODO: 修改文件名
                const fileName = 'compensation'
                // const fileName = `${securityDetail?.userName}_${securityDetail?.id
                //   }_${moment().valueOf()}`
                batchPackDownload(attachment, fileName)
              }}
            >
              打包下载
            </Button>
            // </BusinessPermission>
          }
        >
          <Item field="attachment" />
        </Card>
      </SchemaForm>
    </div>
  )
}

export default StateCenterDecorator()(Compensation)
