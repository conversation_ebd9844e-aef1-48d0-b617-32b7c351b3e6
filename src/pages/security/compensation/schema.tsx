import React from 'react'
import { Observer, SchemaType } from '@amazebird/schema-form'
import { Select, message } from 'antd'
import { BindState } from '@amazebird/utils'
import { textField, numberAmountField, createTextAreaField, attachmentField } from '../utils'

const BindSelect = BindState(Select)

export const getSchema = () => {
  const schema: SchemaType = {
    id: {
      label: '事件ID',
      ...textField,
    },
    department: {
      label: '事件部门',
      ...textField,
    },
    user: {
      label: '事件员工姓名',
      ...textField,
    },
    type: {
      label: '事件类型',
      ...textField,
    },
    responsibilityAllocation: {
      label: '责任划分',
      ...textField,
      visible: Observer({
        watch: 'type',
        action: (_type) => {
          // return type === 'security_event'
          return true
        },
      }),
    },
    compensationType: {
      label: '赔付类型',
      component: (props) => <BindSelect {...props} name="compensationType" />,
      options: [
        { label: '员工赔偿', value: '1' },
        { label: '三者赔偿', value: '2' },
      ],
      props: {
        allowClear: true,
      },
      visible: Observer({
        watch: 'type',
        action: (_type) => {
          // return type === 'security_event'
          return true
        },
      }),
    },
    accidentNature: {
      label: '事故性质',
      component: 'DictSelect',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            // return v?.[0].BM_PAYMENTCO?.list || []
          }
          return []
        },
      }),
      props: {
        mode: 'multiple',
      },
    },
    medicalExpenses: {
      label: '员工医疗费',
      ...numberAmountField,
    },
    workRehabilitationWages: {
      label: '停工留薪期间的工资（含补差）',
      ...numberAmountField,
    },
    oneTimeDisabilityAllowance: {
      label: '一次性伤残补助金（含补差）',
      ...numberAmountField,
    },
    oneTimeWorkInjuryMedicalAllowance: {
      label: '一次性工伤医疗补助金',
      ...numberAmountField,
    },
    oneTimeDisabilityEmploymentAllowance: {
      label: '一次性伤残就业补助金',
      ...numberAmountField,
    },
    employeeTransportationFee: {
      label: '员工交通费',
      ...numberAmountField,
    },
    employeeHospitalizationMealAllowance: {
      label: '员工住院伙食补助费',
      ...numberAmountField,
    },
    employeeDisabilityAids: {
      label: '员工残疾辅助器具',
      ...numberAmountField,
    },
    employeeOtherExpenses: {
      label: '员工其他费用',
      ...numberAmountField,
    },
    thirdPartyMedicalExpenses: {
      label: '三方医疗费',
      ...numberAmountField,
    },
    followUpTreatmentFee: {
      label: '后续治疗费',
      ...numberAmountField,
    },
    thirdPartyHospitalizationMealAllowance: {
      label: '三方住院伙食补助费',
      ...numberAmountField,
    },
    inpatientNursingFee: {
      label: '住院护理费',
      ...numberAmountField,
    },
    outpatientNursingFee: {
      label: '院外护理费',
      ...numberAmountField,
    },
    lostWorkFee: {
      label: '误工费',
      ...numberAmountField,
    },
    nutritionFee: {
      label: '营养费',
      ...numberAmountField,
    },
    thirdPartyTransportationFee: {
      label: '三方交通费',
      ...numberAmountField,
    },
    disabilityCompensation: {
      label: '残疾赔偿金',
      ...numberAmountField,
    },
    thirdPartyDisabilityAids: {
      label: '三方残疾辅助器具',
      ...numberAmountField,
    },
    deathCompensation: {
      label: '死亡赔偿金',
      ...numberAmountField,
    },
    funeralExpenses: {
      label: '丧葬费',
      ...numberAmountField,
    },
    mentalDistressCompensation: {
      label: '精神抚慰金',
      ...numberAmountField,
    },
    dependentLivingExpenses: {
      label: '被抚养人生活费',
      ...numberAmountField,
    },
    appraisalFee: {
      label: '鉴定费用',
      ...numberAmountField,
    },
    directPropertyLoss: {
      label: '直接财产损失',
      ...numberAmountField,
    },
    indirectPropertyLoss: {
      label: '间接财产损失',
      ...numberAmountField,
    },
    thirdPartyOtherExpenses: {
      label: '三方其他费用',
      ...numberAmountField,
    },
    medicalExpensesTotal: {
      label: '医疗费总额',
      component: 'InputNumber',
      mode: 'detail',
    },
    legalCompensationAmount: {
      label: '法定赔偿金额',
      component: 'InputNumber',
      mode: 'detail',
    },
    caseApplicationMediationAmount: {
      label: '案件申请调解金额',
      ...numberAmountField,
    },
    insuranceEstimatedAmount: {
      label: '保险预估金额',
      ...numberAmountField,
    },
    thirdPartyLiabilityAmount: {
      label: '第三方承担金额',
      ...numberAmountField,
    },
    workInjuryInsuranceClaimAmount: {
      label: '工伤保险理赔金额',
      ...numberAmountField,
    },
    employeeEstimatedAmount: {
      label: '员工预估承担金额',
      mode: 'detail',
      component: 'InputNumber',
    },
    companyEstimatedAmount: {
      label: '公司预估承担金额',
      mode: 'detail',
      component: 'InputNumber',
    },
    employeeBorrowAmount: {
      label: '员工已借金额',
      mode: 'detail',
      component: 'InputNumber',
    },
    companyAdvanceAmount: {
      label: '公司已垫付金额',
      mode: 'detail',
      component: 'InputNumber',
    },
    compensationDescription: createTextAreaField('赔偿情况说明'),
    attachment: {
      ...attachmentField,
      label: '',
    },
  }
  return schema
}
