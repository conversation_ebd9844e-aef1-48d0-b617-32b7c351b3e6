import React, { ReactNode } from 'react'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import { Card } from 'antd'
import { SchemaType } from '@amazebird/schema-form'
import '@amazebird/antd-business-field'

interface SecurityFormWrapperProps {
  /** 页面名称，用于生成唯一的表单项配置 */
  pageName: string
  /** 表单Schema */
  schema: SchemaType
  /** 表单样式类名 */
  className?: string
  /** 表单值变化回调 */
  onValuesChange?: (changedValues: any, allValues: any) => void
  /** 子组件 */
  children?: ReactNode
  /** 是否显示附件卡片 */
  showAttachmentCard?: boolean
  /** 附件卡片标题 */
  attachmentTitle?: string
  /** 附件卡片额外操作 */
  attachmentExtra?: ReactNode
  /** 附件字段名 */
  attachmentFieldName?: string
  /** 附件字段配置 */
  attachmentFieldConfig?: any
}

const SecurityFormWrapper: React.FC<SecurityFormWrapperProps> = ({
  schema,
  className,
  onValuesChange,
  children,
  showAttachmentCard = true,
  attachmentTitle = '附件',
  attachmentExtra,
  attachmentFieldName = 'attachment',
  attachmentFieldConfig,
}) => {
  const form = SchemaForm.createForm()

  return (
    <div>
      <SchemaForm
        schema={schema}
        layout="horizontal"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 10 }}
        form={form}
        className={className}
        onValuesChange={onValuesChange}
      >
        {children}

        {showAttachmentCard && (
          <Card title={attachmentTitle} size="default" key="attachment" extra={attachmentExtra}>
            <Item field={attachmentFieldName} {...attachmentFieldConfig} />
          </Card>
        )}
      </SchemaForm>
    </div>
  )
}

export default SecurityFormWrapper
