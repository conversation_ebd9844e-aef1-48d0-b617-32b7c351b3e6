import React, { useMemo } from 'react'
import { SchemaForm, Item, FormItemGrid } from '@amazebird/antd-schema-form'
import { SchemaType } from '@amazebird/schema-form'
import { createFormItemConfig } from '../utils'
import '@amazebird/antd-business-field'

interface SimpleFormField {
  /** 字段名 */
  name: string
  /** 字段配置覆盖 */
  config?: any
}

interface SimpleSecurityFormProps {
  /** 页面名称，用于生成唯一的表单项配置 */
  pageName: string
  /** 表单Schema */
  schema: SchemaType
  /** 表单样式类名 */
  className?: string
  /** 表单值变化回调 */
  onValuesChange?: (changedValues: any, allValues: any) => void
  /** 基础字段列表 */
  basicFields: SimpleFormField[]
  /** 描述字段配置 */
  descriptionField?: {
    name: string
    config?: any
  }
  /** 附件字段配置 */
  attachmentField?: {
    name: string
    config?: any
  }
  /** 列数 */
  colCount?: number
  /** 内容包装器样式类名 */
  wrapperClassName?: string
}

const SimpleSecurityForm: React.FC<SimpleSecurityFormProps> = ({
  pageName,
  schema,
  className,
  onValuesChange,
  basicFields,
  descriptionField,
  attachmentField,
  colCount = 2,
  wrapperClassName,
}) => {
  const form = SchemaForm.createForm()
  const generateFormItemConfig = useMemo(() => createFormItemConfig(pageName), [pageName])

  return (
    <div>
      <SchemaForm
        schema={schema}
        layout="horizontal"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 10 }}
        form={form}
        className={className}
        onValuesChange={onValuesChange}
      >
        <div className={wrapperClassName}>
          {/* 基础字段网格 */}
          <FormItemGrid colCount={colCount} fillEmpty>
            {basicFields.map((field) => (
              <Item
                key={field.name}
                {...generateFormItemConfig(field.name)}
                {...field.config}
              />
            ))}
          </FormItemGrid>
          
          {/* 描述字段 */}
          {descriptionField && (
            <Item
              {...generateFormItemConfig(descriptionField.name)}
              wrapperCol={{ span: 17 }}
              labelCol={{ span: 4 }}
              {...descriptionField.config}
            />
          )}
          
          {/* 附件字段 */}
          {attachmentField && (
            <Item
              field={attachmentField.name}
              wrapperCol={{ span: 17 }}
              labelCol={{ span: 4 }}
              {...attachmentField.config}
            />
          )}
        </div>
      </SchemaForm>
    </div>
  )
}

export default SimpleSecurityForm
