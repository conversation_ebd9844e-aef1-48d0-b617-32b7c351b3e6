import React from 'react'
import { Modal, Form, DatePicker, Typography, Select } from 'antd'
import dayjs from 'dayjs'

const { Text } = Typography

interface Iprops {
  visible: boolean
  record: any
  onOk?: (values: any) => void
  close?: () => void
  dict: any
  type: 'close' | 'withdraw'
}

const CaseModal: React.FC<Iprops> = (props) => {
  const { visible, record, onOk, close, type, dict } = props
  const [form] = Form.useForm()

  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      console.log('close values:', values)
      onOk?.(values)
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    close?.()
  }

  return (
    <Modal
      title={`${type === 'close' ? '结案' : '撤案'}-${record?.userName}（${record?.userNum}）`}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText="确定"
      cancelText="取消"
      destroyOnClose
    >
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <Form.Item label={type === 'close' ? '结案日期' : '撤案日期'} name="closeDate">
          {type === 'close' ? (
            <DatePicker style={{ width: '100%' }} placeholder="请选择结案日期" />
          ) : (
            <Text>{dayjs().format('YYYY-MM-DD')}</Text>
          )}
        </Form.Item>
        {type === 'withdraw' && (
          <Form.Item label="撤案原因" name="withdrawReason">
            <Select options={dict?.BM_CANCELREASON?.list || []} placeholder="请选择撤案原因" />
          </Form.Item>
        )}
      </Form>
    </Modal>
  )
}

export default CaseModal
