import React, { useEffect, useState, useMemo } from 'react'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import { Row, Col, Card, Button, Space, message, Divider, Dropdown } from 'antd'
import { Anchor as DhrAnchor } from '@amazebird/editor-component'
import { map, pickBy, pick, keys } from 'lodash-es'
import {
  getDminStaffRelationV1SecurityEventById as getDetailById,
  putDminStaffRelationV1SecurityEventById as editDetail,
} from '@/api/securityEventAdminController'
import qs from 'query-string'
import '@amazebird/antd-business-field'
import { useNavigate } from 'react-router-dom'
import Footer from '@/components/baseContainer/Footer'
import moment from 'moment'
import PageLoading from '@/components/pageLoading'
import useBatchPackDownload from '@/hooks/useBatchPackDownload'
import { useStore } from '@/stores'
import {
  generateFormItemConfig,
  scrollToFirstErrorField,
} from '@/pages/employment-dispute/components/DisputeForm/utils'
import BusinessPermission from '@/components/businessPermission'
import { SECURITY } from '@/constants/rbac-code/security'
import { DownOutlined } from '@ant-design/icons'
import { useSecurityOperations } from '../../hooks/useSecurityOperations'
import { getSchema } from './schema'
import style from './style.module.less'
import AdvanceManagementTable from './AdvanceManagementTable'
import RepaymentManagementTable from './RepaymentManagementTable'

interface Iprops {
  dict: any
}

const Schedule = (props: Iprops) => {
  const { dict } = props
  const { dhrDict, ehrDict, ucDict } = dict
  const { id } = qs.parse(window.location.search)
  const form: any = SchemaForm.createForm()
  const navigate = useNavigate()
  const [footer, setFooter] = useState<React.ReactNode>(null)
  const [loading, setLoading] = useState(true)
  const [commitLoading, setCommitLoading] = useState(false)
  const [securityDetail, setSecurityDetail] = useState<any>()
  const [hasAttachment, setHasAttachment] = useState(false)
  const { batchPackDownload } = useBatchPackDownload()
  const setTitle = useStore((state) => state.setTitle)
  const setExtraElement = useStore((state) => state.setExtra)
  const { operatorClick } = useSecurityOperations({ record: securityDetail, dict: ehrDict })
  const basicProps = {
    labelCol: { flex: '130px' },
  }

  const handleSave = async () => {
    try {
      const values = await form?.validateFields()
      const data = {
        ...values,
        id,
        accidentDurationInCompany: values.accidentDurationInCompany
          ? values.accidentDurationInCompany?.replace('年', '')
          : undefined,
        followPeriod: values.followPeriod ? values.followPeriod.replace('天', '') : undefined,
        timeAccident: values.timeAccident ? moment(values.timeAccident).valueOf() : undefined,
        claimPaidDate: values.claimPaidDate ? moment(values.claimPaidDate).valueOf() : undefined,
        completeClaimMaterialPostDate: values.completeClaimMaterialPostDate
          ? moment(values.completeClaimMaterialPostDate).valueOf()
          : undefined,
        closeDate: values.closeDate ? moment(values.closeDate).valueOf() : undefined,
        suspendDate: values.suspendDate ? moment(values.suspendDate).valueOf() : undefined,
        startRecoverDate: values.startRecoverDate
          ? moment(values.startRecoverDate).valueOf()
          : undefined,
        rewardPunishDate: values.rewardPunishDate
          ? moment(values.rewardPunishDate).valueOf()
          : undefined,
        userNum: values.userIdShopFollowPrincipal
          ? values.userIdShopFollowPrincipal.value
          : undefined,
        attachment: values.attachment
          ? JSON.stringify(map(values.attachment, (item) => pick(item, ['uuid', 'name'])))
          : undefined,
        userIdResponsibleManager: values.userIdResponsibleManager
          ? values.userIdResponsibleManager.value
          : undefined,
        userIdShopFollowPrincipal: values.userIdShopFollowPrincipal
          ? values.userIdShopFollowPrincipal.value
          : undefined,
      }
      setCommitLoading(true)
      await editDetail(data)
      message.success('修改成功')
      navigate(-1)
    } catch (error) {
      scrollToFirstErrorField(form, error)
    }
    setCommitLoading(false)
  }

  const handleCancel = () => {
    navigate(-1)
  }

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      if (changedValues[key] && changedValues[key].length > 0 && !hasAttachment) {
        setHasAttachment(true)
      } else if ((!changedValues[key] || changedValues[key].length <= 0) && hasAttachment) {
        setHasAttachment(false)
      }
    }
  }

  useEffect(() => {
    setLoading(true)

    setFooter(
      <Space>
        <Button style={{ marginRight: 20 }} onClick={handleCancel}>
          取消
        </Button>
        <Button type="primary" onClick={handleSave} loading={commitLoading}>
          提交
        </Button>
      </Space>,
    )
    getDetailById({ id })
      .then((res) => {
        const { data } = res
        if (data) {
          const attachment = data.attachment ? JSON.parse(data.attachment) : undefined
          form.setFieldsValue({
            ...pickBy(data, (v) => v !== ''),
            timeHire: data.timeHire ? moment(data.timeHire).format('YYYY-MM-DD') : '--',
            userNameUpdate: data.userNameUpdate
              ? `${data.userNameUpdate}${
                  data.userIdUpdate !== '00000' ? `(${data.userIdUpdate})` : ''
                }`
              : '--',
            timeCreate: data.timeCreate ? moment(data.timeCreate).format('YYYY-MM-DD') : '--',
            timeUpdate: data.timeUpdate ? moment(data.timeUpdate).format('YYYY-MM-DD') : '--',
            followPeriod: data.followPeriod ? `${data.followPeriod}天` : '--',
            timeAccident: data.timeAccident
              ? moment(data.timeAccident).format('YYYY-MM-DD')
              : undefined,
            claimPaidDate: data.claimPaidDate
              ? moment(data.claimPaidDate).format('YYYY-MM-DD')
              : undefined,
            completeClaimMaterialPostDate: data.completeClaimMaterialPostDate
              ? moment(data.completeClaimMaterialPostDate).format('YYYY-MM-DD')
              : undefined,
            closeDate: data.closeDate ? moment(data.closeDate).format('YYYY-MM-DD') : undefined,
            suspendDate: data.suspendDate
              ? moment(data.suspendDate).format('YYYY-MM-DD')
              : undefined,
            startRecoverDate: data.startRecoverDate
              ? moment(data.startRecoverDate).format('YYYY-MM-DD')
              : undefined,
            rewardPunishDate: data.rewardPunishDate
              ? moment(data.rewardPunishDate).format('YYYY-MM-DD')
              : undefined,
            userIdResponsibleManager: data.userIdResponsibleManager
              ? {
                  label: data.userIdResponsibleManagerName,
                  value: data.userIdResponsibleManager,
                }
              : undefined,
            userIdShopFollowPrincipal: data.userIdShopFollowPrincipal
              ? {
                  label: data.userIdShopFollowPrincipalName,
                  value: data.userIdShopFollowPrincipal,
                }
              : undefined,
            attachment,
          })
          const title = data?.userName && data?.userNum ? `${data?.userName}(${data?.userNum})` : ''
          if (title) {
            setTitle(title)
          }
          setSecurityDetail(data)
          setHasAttachment(attachment?.length > 0)
        }
      })
      .finally(() => {
        setLoading(false)
      })

    return () => {
      setFooter(null)
    }
  }, [])

  useEffect(() => {
    if (dhrDict) {
      form.setState({ dhrDict })
    }
    if (ehrDict) {
      form.setState({ ehrDict })
    }
  }, [ehrDict, dhrDict])

  useEffect(() => {
    setExtraElement(
      <>
        <Button type="primary" ghost onClick={() => operatorClick('compensation_apply')}>
          赔偿额度申请
        </Button>
        <Button type="primary" ghost onClick={() => operatorClick('payment_apply')}>
          安全事件付款申请
        </Button>
        <Dropdown
          menu={{
            items: [
              {
                label: '安全事件借款申请',
                key: 'loan_apply',
              },
              {
                label: '回款审批',
                key: 'repayment_approval',
              },
              {
                label: '不追偿申请',
                key: 'no_repayment_apply',
              },
              {
                label: '升级至安全争议',
                key: 'upgrade_to_dispute',
              },
              {
                label: '结案',
                key: 'close',
              },
              {
                label: '撤案',
                key: 'withdraw',
              },
              {
                label: '取消撤案',
                key: 'cancel_withdraw',
              },
            ],
            onClick: ({ key }) => operatorClick(key),
          }}
        >
          <Button type="primary" ghost>
            <Space>
              更多
              <DownOutlined />
            </Space>
          </Button>
        </Dropdown>
      </>,
    )

    return () => {
      setExtraElement(null)
    }
  }, [securityDetail, operatorClick])

  const schema = useMemo(() => getSchema({ ucDict }), [ucDict])

  return loading ? (
    <PageLoading style={{ minHeight: 'calc(100vh - 300px)', backgroundColor: '#fff' }} />
  ) : (
    <div>
      <SchemaForm
        schema={schema}
        layout="horizontal"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 10 }}
        form={form}
        className={style.scheduleForm}
        onValuesChange={onValuesChange}
      >
        <Col span="24">
          <div className={style.employeeDetail}>
            <Row>
              <Col span="24">
                <Row gutter={0} align="top">
                  <Col span={6}>
                    <Item {...generateFormItemConfig('contact')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('positionName')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('positionType')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('departmentLevelFourth')} {...basicProps} />
                  </Col>
                </Row>
              </Col>
              <Col span="24">
                <Row gutter={0} align="top">
                  <Col span={6}>
                    <Item {...generateFormItemConfig('timeHire')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('timeQuit')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('timeCreate')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('userNameUpdate')} {...basicProps} />
                  </Col>
                </Row>
              </Col>
              <Col span="24" className={style.lastCol}>
                <Row gutter={0} align="top">
                  <Col span={6}>
                    <Item {...generateFormItemConfig('timeUpdate')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('oaNo')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('id')} {...basicProps} />
                  </Col>
                </Row>
              </Col>
            </Row>
          </div>
        </Col>
        <Row>
          <Col span="24">
            <div className={style.anchorWrapper}>
              <DhrAnchor
                options={[
                  { title: '案件信息', message: '' },
                  { title: '申报保险信息', message: '' },
                  { title: '进度管控', message: '' },
                  { title: '费用管理' },
                  { title: '借支管理' },
                  { title: '奖惩信息' },
                  { title: '附件' },
                ]}
              >
                <Card title="案件信息" size="default" key="information">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentYearMonth')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentDurationInCompany')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentDurationCategory')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentArea')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('eventLocation')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('timeAccident')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('dateCategory')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('eventCategory')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('eventLevel')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('weather')} />
                      </Col>
                    </Row>
                    <Item
                      {...generateFormItemConfig('accidentContent')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('alarmUser')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('userIdResponsibleManager')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentNature')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isThirdPartyInvolved')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyName')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyAge')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyContact')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('responsibilityAllocation')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentScenario')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('injuredPersonnelCondition')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('staffInjuryType')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyInjuryType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyPropertyLoss')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyVehicleLoss')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('eventCoefficient')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('eventCoefficientChangeReason')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('deliveryOrderNum')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('followCycleCategory')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentContractSubjectName')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentContractType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('currentContractSubjectName')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('currentContractType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentDepartmentName')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentPositionName')} />
                      </Col>
                    </Row>
                    <Item
                      {...generateFormItemConfig('remark')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                    <Item
                      {...generateFormItemConfig('remarkOther')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                    <Item
                      {...generateFormItemConfig('remarkOtherTwo')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                  </>
                </Card>

                <Card title="申报保险信息" size="default" key="InsuranceInformation">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('reportUnit')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('insureType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('claimUser')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isApplyWorkInjuryRecognition')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('completeClaimMaterialPostDate')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('claimPaidDate')} />
                      </Col>
                    </Row>
                  </>
                </Card>

                <Card title="进度管控" size="default" key="schedule">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('userIdShopFollowPrincipal')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('process')} />
                      </Col>
                    </Row>
                    <Item
                      {...generateFormItemConfig('processRemark')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isWithdraw')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('withdrawReason')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('closeDate')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('followStatus')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('followPeriod')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('followPeriodCategory')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('unclosedReason')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isWorkInjury')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isApplyLaborAbilityIdentification')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('disabilityLevel')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isSignedAttachedBook')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isReportInsurance')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isApplyWorkInjuryCompensation')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isSuspend')} />
                      </Col>
                    </Row>
                    <Row>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('suspendDate')} />
                      </Col>
                    </Row>
                    <Item
                      {...generateFormItemConfig('suspendReason')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                  </>
                </Card>

                <Card title="费用管理" size="default" key="costManagement">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('estimatedAmount')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('closeAmount')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('actualPayUnit')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('payType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('insuranceCompanyPayAmount')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('pupuPayAmount')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('staffPayAmount')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyPayAmount')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isRecovery')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('recoveryDetails')} />
                      </Col>
                    </Row>

                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('actualPayUnit1')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('payType1')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('insuranceCompanyPayAmount1')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('actualPayUnit2')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('payType2')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('insuranceCompanyPayAmount2')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('actualPayUnit3')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('payType3')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('insuranceCompanyPayAmount3')} />
                      </Col>
                    </Row>
                  </>
                </Card>

                <Card title="借支管理" size="default" key="borrowManagement">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isBorrow')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top" justify="center" style={{ marginBottom: 32 }}>
                      <Col span={18}>
                        <AdvanceManagementTable />
                      </Col>
                    </Row>
                    <Divider />
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('currentDebt')} />
                      </Col>
                    </Row>

                    <Row gutter={0} align="top" justify="center" style={{ marginBottom: 32 }}>
                      <Col span={18}>
                        <RepaymentManagementTable />
                      </Col>
                    </Row>
                  </>
                </Card>

                <Card title="奖惩信息" size="default" key="rewardInformation">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <>
                          <Item {...generateFormItemConfig('isStaffPenalty')} />
                        </>
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('penaltyType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('noPenaltyReasonDetails')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('rewardPunishDate')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('rewardPunishType')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('rewardPunishCategory')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <>
                          <Item {...generateFormItemConfig('rewardPunishProgress')} />
                          <Item {...generateFormItemConfig('noPenaltyReason')} />
                        </>
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('notImplementedRewardPunishReason')} />
                      </Col>
                    </Row>
                  </>
                </Card>
                <Card
                  title="附件"
                  size="default"
                  key="appendix"
                  extra={
                    <BusinessPermission code={SECURITY.BATCH_DOWNLOAD}>
                      <Button
                        type="link"
                        disabled={!hasAttachment}
                        onClick={() => {
                          const attachment = form.getFieldValue('attachment')
                          const fileName = `${securityDetail?.userName}_${
                            securityDetail?.id
                          }_${moment().valueOf()}`
                          batchPackDownload(attachment, fileName)
                        }}
                      >
                        打包下载
                      </Button>
                    </BusinessPermission>
                  }
                >
                  <Item field="attachment" />
                </Card>
              </DhrAnchor>
            </div>
          </Col>
        </Row>
      </SchemaForm>
      <Footer>{footer}</Footer>
    </div>
  )
}

export default Schedule
