import React from 'react'
import { Observer, SchemaType } from '@amazebird/antd-schema-form'
import { CLOSE_CASE_HIDE_VALUE, OSS_CONFIG, SECURITY_EVENT_PROCESS, UC_DICT_KEY } from '@/constants'
import { map, filter, isNil, isObject, findIndex, keys } from 'lodash-es'
import { message, Typography } from 'antd'
import moment from 'moment'
import { calcYear } from './utils'

const Empty = () => <span>--</span>

type EllipsisTextProps = {
  value?: any
  format?: string
  options?: any[]
  ellipsis?: boolean
  [key: string]: any
}

const { Text } = Typography

const EllipsisText = (props: EllipsisTextProps) => {
  const ellipsis = isNil(props?.ellipsis) ? true : props.ellipsis
  let text = props.value
  if (props?.format && text) {
    text = moment(props.value).format(props?.format)
  } else if (props?.options && props.options.length > 0) {
    text = map(
      filter(props?.options, (item) => item.value === props.value),
      (item) => item.label,
    ).join('；')
  } else if (isObject(text) && 'label' in text) {
    text = text.label
  }
  return <Text ellipsis={ellipsis ? { tooltip: text || '--' } : false}>{text || '--'}</Text>
}

const textField = {
  mode: 'detail',
  renderItem: () => (props) => {
    return <EllipsisText {...props} />
  },
}

export const getSchema = (extra?: Record<string, any>) => {
  const schema: SchemaType = {
    contact: {
      component: 'Input',
      label: '联系方式',
      ...textField,
    },
    positionName: {
      component: 'Input',
      label: '岗位',
      ...textField,
    },
    positionType: {
      component: 'DictSelect',
      label: '岗位类型划分',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_PostType?.list || []
          }
          return []
        },
      }),
      mode: 'detail',
    },
    departmentLevelFourth: {
      component: 'Input',
      label: '四级部门',
      ...textField,
    },
    timeHire: {
      component: 'Input',
      label: '入职日期',
      ...textField,
    },
    timeQuit: {
      component: 'Input',
      label: '离职日期',
      ...textField,
    },
    timeCreate: {
      component: 'Input',
      label: '事件生成日期',
      ...textField,
    },
    userNameUpdate: {
      component: 'Input',
      label: '最后操作人',
      ...textField,
    },
    timeUpdate: {
      component: 'Input',
      label: '最后操作时间',
      ...textField,
    },
    oaNo: {
      component: 'Input',
      label: '危机管理报告单号',
      ...textField,
    },
    id: {
      component: 'Input',
      label: '事件ID',
      ...textField,
    },
    accidentContent: {
      component: 'Input.TextArea',
      label: '案件内容',
      required: true,
      props: {
        showCount: false,
      },
    },
    accidentNature: {
      component: 'DictSelect',
      label: '事故性质',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].THE_ACCIDENT_NATURE?.list || []
          }
          return []
        },
      }),
      required: true,
      props: {
        mode: 'multiple',
      },
    },
    accidentScenario: {
      component: 'DictSelect',
      label: '案发场景',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_OCCURSENCE?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    actualPayUnit: {
      component: 'DictSelect',
      label: '实际赔付单位',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_PAYMENTCO?.list || []
          }
          return []
        },
      }),
      props: {
        mode: 'multiple',
      },
    },
    alarmUser: {
      component: 'Input',
      label: '报警人',
    },
    claimPaidDate: {
      component: 'DatePicker',
      label: '理赔到账日期',
    },
    claimUser: {
      component: 'Input',
      label: '理赔员',
      props: {
        maxLength: 30,
      },
    },
    closeAmount: {
      component: 'InputNumber',
      label: '结案金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    closeDate: {
      component: 'DatePicker',
      label: '结案日期',
    },
    followStatus: {
      component: Observer({
        watch: 'process',
        action: (process) => {
          if (process === SECURITY_EVENT_PROCESS.inProgress) {
            return 'Text'
          }
          return Empty
        },
      }),
      label: '跟进状态',
    },
    companyRepayAmount: {
      component: 'InputNumber',
      label: '公司还款金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    completeClaimMaterialPostDate: {
      component: 'DatePicker',
      label: '保险公司收到完整理赔材料日期',
    },
    currentDebt: {
      component: 'InputNumber',
      mode: 'detail',
      label: '员工未还金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    deliveryOrderNum: {
      component: 'Input',
      label: '配送订单号',
      props: {
        maxLength: 30,
      },
    },
    disabilityLevel: {
      component: 'DictSelect',
      label: '伤残等级',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_INJURE_DEGREE?.list || []
          }
          return []
        },
      }),
    },
    staffRepayAmount: {
      component: 'InputNumber',
      label: '员工还款金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    staffTotalDebt: {
      component: 'InputNumber',
      label: '员工总欠款金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    estimatedAmount: {
      component: 'InputNumber',
      label: '预估金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
      required: true,
    },
    eventCoefficientChangeReason: {
      component: 'Input',
      label: '案件系数变更原因',
      props: {
        maxLength: 100,
      },
    },
    eventCoefficient: {
      component: 'DictSelect',
      label: '案件系数',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_AJXS?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    eventLevel: {
      component: 'DictSelect',
      label: '事件等级',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].EVENT_LEVEL?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    injuredPersonnelCondition: {
      component: 'DictSelect',
      label: '受伤人员情况',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_INJURYSTATUS?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    insuranceCompanyPayAmount: {
      component: 'InputNumber',
      label: '保险公司赔付金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    insuranceRepayAmount: {
      component: 'InputNumber',
      label: '保险还款金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    insureType: {
      component: 'DictSelect',
      label: '申报险种',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BX_SFXZ?.list || []
          }
          return []
        },
      }),
      required: true,
      props: {
        mode: 'multiple',
      },
    },
    isApplyLaborAbilityIdentification: {
      component: 'Select',
      label: '是否申请劳动能力鉴定',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isApplyWorkInjuryCompensation: {
      component: 'Select',
      label: '是否申请工伤待遇赔偿',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isApplyWorkInjuryRecognition: {
      component: 'Select',
      label: '是否申请认定工伤',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      required: true,
    },
    isBorrow: {
      component: 'Select',
      label: '是否借支',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isRecovery: {
      component: 'Select',
      label: '是否追偿',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isReportInsurance: {
      component: 'Select',
      label: '是否报商保',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isSignedAttachedBook: {
      component: 'Select',
      label: '是否签核具结书',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isStaffPenalty: {
      component: 'Select',
      label: '出险员工是否处罚',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isSuspend: {
      component: 'Select',
      label: '是否挂起',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isThirdPartyInvolved: {
      component: 'Select',
      label: '是否涉及第三方',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      required: true,
    },
    isWithdraw: {
      component: 'Select',
      label: '是否撤案',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isWorkInjury: {
      component: 'Select',
      label: '是否认定为工伤',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    notImplementedRewardPunishReason: {
      component: 'DictSelect',
      label: '未实行奖惩原因',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_250?.list || []
          }
          return []
        },
      }),
    },
    payType: {
      component: 'DictSelect',
      label: '赔付险种',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_PAY_CATEGORY?.list || []
          }
          return []
        },
      }),
    },
    penaltyType: {
      component: 'DictSelect',
      label: '处罚类型',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].TYPE_OF_PENALTY?.list || []
          }
          return []
        },
      }),
    },
    process: {
      component: 'DictSelect',
      label: '进度',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BX_AJJD?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    processRemark: {
      component: 'Input.TextArea',
      label: '进度说明',
      props: {
        maxLength: 1000,
      },
    },
    pupuPayAmount: {
      component: 'InputNumber',
      label: '朴朴赔付金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    recoverAmount: {
      component: 'InputNumber',
      label: '返还金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    recoverMethod: {
      component: 'Input',
      label: '返还方式',
      props: {
        maxLength: 100,
      },
    },
    recoveredAmount: {
      component: 'InputNumber',
      label: '已返还金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    recoveryDetails: {
      component: 'Input',
      label: '追偿详情',
      props: {
        maxLength: 100,
      },
    },
    remark: {
      component: 'Input.TextArea',
      label: '案件备注',
      props: {
        maxLength: 1000,
      },
    },
    remarkOther: {
      component: 'Input.TextArea',
      label: '案件备注1',
      props: {
        maxLength: 1000,
      },
    },
    remarkOtherTwo: {
      component: 'Input.TextArea',
      label: '案件备注2',
      props: {
        maxLength: 1000,
      },
    },
    repayProgress: {
      component: 'DictSelect',
      label: '还款进度',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_REPAYSCHE?.list || []
          }
          return []
        },
      }),
    },
    reportUnit: {
      component: 'DictSelect',
      label: '申报单位',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_REPORT_CO?.list || []
          }
          return []
        },
      }),
      required: true,
      props: {
        mode: 'multiple',
      },
    },
    responsibilityAllocation: {
      component: 'DictSelect',
      label: '责任划分',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_RESPONSIBILITY?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    rewardPunishCategory: {
      component: 'DictSelect',
      label: '奖惩类别',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].REWARD_PUNISHMENT_CATEGORIES?.list || []
          }
          return []
        },
      }),
    },
    rewardPunishDate: {
      component: 'DatePicker',
      label: '奖惩日期',
    },
    rewardPunishProgress: {
      component: 'DictSelect',
      label: '奖惩进度',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_249?.list || []
          }
          return []
        },
      }),
    },
    rewardPunishType: {
      component: 'DictSelect',
      label: '奖惩类型',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_56?.list || []
          }
          return []
        },
      }),
    },
    staffInjuryType: {
      component: 'DictSelect',
      label: '员工受伤类型',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_WOUNDTYPE?.list || []
          }
          return []
        },
      }),
    },
    staffPayAmount: {
      component: 'InputNumber',
      label: '员工赔付金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    startRecoverDate: {
      component: 'DatePicker',
      label: '开始返还日期',
    },
    suspendDate: {
      component: 'DatePicker',
      label: '挂起日期',
    },
    suspendReason: {
      component: 'Input.TextArea',
      label: '挂起原因',
      props: {
        maxLength: 1000,
      },
    },
    thirdPartyAge: {
      component: 'Input',
      label: '第三方年龄',
      props: {
        maxLength: 30,
      },
    },
    thirdPartyContact: {
      component: 'Input',
      label: '第三方联系方式',
      pattern: /^[0-9]*$/,
      props: {
        maxLength: 11,
      },
    },
    thirdPartyInjuryType: {
      component: 'DictSelect',
      label: '第三方受伤类型',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_WOUNDTYPE?.list || []
          }
          return []
        },
      }),
    },
    thirdPartyName: {
      component: 'Input',
      label: '第三方姓名',
      props: {
        maxLength: 30,
      },
    },
    thirdPartyPayAmount: {
      component: 'InputNumber',
      label: '第三方赔付金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    thirdPartyPropertyLoss: {
      component: 'Input',
      label: '第三方财产损失情况',
      props: {
        maxLength: 100,
      },
    },
    thirdPartyVehicleLoss: {
      component: 'Input',
      label: '第三方车辆损失情况',
      props: {
        maxLength: 100,
      },
    },
    timeAccident: {
      component: 'DatePicker',
      label: '事故日期',
      required: true,
      props: {
        format: 'YYYY-MM-DD',
      },
    },
    totalBorrowAmount: {
      component: 'InputNumber',
      label: '总借支金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    unclosedReason: {
      component: 'DictSelect',
      label: '未结案原因',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            const filterList = v?.[0].BM_CLOSE_CASE?.list.map((item) => {
              if (CLOSE_CASE_HIDE_VALUE.indexOf(item.code) !== -1) {
                return {
                  ...item,
                  isEnabled: false,
                }
              }
              return item
            })
            return filterList || []
          }
          return []
        },
      }),
    },
    userIdResponsibleManager: {
      component: 'UserSelector',
      label: '责任店长',
      props: {
        showSearch: true,
        onlySearch: true,
      },
    },
    userIdShopFollowPrincipal: {
      component: 'UserSelector',
      label: '门店跟进负责人',
      props: {
        showSearch: true,
        onlySearch: true,
      },
    },
    weather: {
      component: 'DictSelect',
      label: '天气',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].THE_WEATHER?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    withdrawReason: {
      component: 'DictSelect',
      label: '撤案原因',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_CANCELREASON?.list || []
          }
          return []
        },
      }),
    },
    accidentYearMonth: {
      component: 'Input',
      label: '案发年月',
      ...textField,
    },
    accidentDurationInCompany: {
      component: 'Input',
      label: '案发在司时长',
      ...textField,
      value: Observer({
        watch: ['timeAccident', 'timeHire'],
        action: async (value) => {
          const timeAccidentValue = moment(value[0]).valueOf()
          const timeHireMomentValue = moment(value[1]).valueOf()
          return calcYear(timeAccidentValue, timeHireMomentValue)
        },
      }),
    },
    accidentDurationCategory: {
      component: 'Input',
      label: '在司时长分类',
      ...textField,
      value: Observer({
        watch: 'accidentDurationInCompany',
        action: async (accidentDurationInCompany) => {
          if (isNil(accidentDurationInCompany)) {
            return '--'
          }
          const value = Number(accidentDurationInCompany.replace('年', ''))
          if (value >= 0 && value < 0.083) {
            return '1个月以内'
          }
          if (value >= 0.083 && value < 0.25) {
            return '1-3个月'
          }
          if (value >= 0.25 && value < 0.5) {
            return '3-6个月'
          }
          if (value >= 0.5 && value < 1) {
            return '6个月-1年'
          }
          if (value >= 1 && value < 2) {
            return '1-2年'
          }
          if (value >= 2) {
            return '2年以上'
          }
          return '--'
        },
      }),
    },
    accidentArea: {
      component: 'DictSelect',
      label: '案发区域',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_BZGSQY?.list || []
          }
          return []
        },
      }),
      mode: 'detail',
    },
    eventLocation: {
      component: 'Input',
      label: '事件发生地点',
      ...textField,
    },
    dateCategory: {
      component: 'DictSelect',
      label: '时间分类',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].TIME_CLASSIFICATION?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    eventCategory: {
      component: 'DictSelect',
      label: '事件分类',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].EVENT_CLASSIFICATION?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    followCycleCategory: {
      component: 'Input',
      label: '跟进周期分类',
      mode: 'detail',
      value: Observer({
        watch: ['eventCoefficient', 'followPeriod'],
        action: async (value) => {
          const followPeriod = Number(value[1].replace('天', ''))
          if ((value[0] === '01' || value[0] === '02') && followPeriod > 90) {
            return '超过时限要求'
          }
          if (value[0] === '03' && followPeriod > 130) {
            return '超过时限要求'
          }
          if (value[0] === '04' && followPeriod > 180) {
            return '超过时限要求'
          }
          if (!value[0]) {
            return '--'
          }
          return '在时限要求内'
        },
      }),
    },
    // accidentContractSubject: {
    //   component: 'DictSelect',
    //   label: '案发合同主体',
    //   mode: 'detail',
    //   options: Observer({
    //     watch: ['#ehrDict'],
    //     action: (v) => {
    //       if (v && v.length > 0 && v?.[0]) {
    //         return v?.[0].BM_HTZT?.list || []
    //       }
    //       return []
    //     },
    //   }),
    // },
    accidentContractSubjectName: {
      component: 'Text',
      label: '案发合同主体',
      mode: 'detail',
    },
    accidentContractType: {
      component: 'DictSelect',
      label: '案发合同主体类型',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_VAA?.list || []
          }
          return []
        },
      }),
      mode: 'detail',
    },
    currentContractSubjectName: {
      component: 'Text',
      label: '当前合同主体',
      mode: 'detail',
    },
    currentContractType: {
      component: 'DictSelect',
      label: '当前合同主体类型',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_VAA?.list || []
          }
          return []
        },
      }),
      mode: 'detail',
    },
    accidentDepartmentName: {
      component: 'Input',
      label: '案发部门名称',
      ...textField,
    },
    accidentPositionName: {
      component: 'Input',
      label: '案发岗位名称',
      ...textField,
    },
    followPeriod: {
      component: 'Input',
      label: '跟进时长',
      ...textField,
      value: Observer({
        watch: ['timeAccident', 'process', 'closeDate'],
        action: async (value) => {
          const timeAccidentValue = moment(value[0])
          const closeDateValue = moment(value[2])
          if (value[0] && value[1] === '1') {
            return `${moment().diff(timeAccidentValue, 'day')}天`
          }
          if (value[0] && (value[1] === '2' || value[1] === '3') && value[2]) {
            return `${closeDateValue.diff(timeAccidentValue, 'day')}天`
          }
          return '--'
        },
      }),
    },
    followPeriodCategory: {
      component: 'Text',
      label: '跟进时长分类',
      mode: 'detail',
      value: Observer({
        watch: 'followPeriod',
        action: async (followPeriod) => {
          if (isNil(followPeriod)) {
            return '--'
          }
          const value = Number(followPeriod.replace('天', ''))
          // A. 0＜跟进时长≤90时，类型为【0-3个月】；
          // B. 90＜跟进时长≤180时，类型为【3-6个月】；
          // C. 180<跟进时长＜365时，类型为【6-12个月】；
          // D. 365≤跟进时长＜730时，类型为【12-24个月】；
          // E. 跟进时长≥730时，类型为【24个月以上】。
          if (value > 0 && value <= 90) {
            return '0-3个月'
          }
          if (value > 90 && value <= 180) {
            return '3-6个月'
          }
          if (value > 180 && value < 365) {
            return '6-12个月'
          }
          if (value >= 365 && value < 730) {
            return '12-24个月'
          }
          if (value >= 730) {
            return '24个月以上'
          }
          return '--'
        },
      }),
    },
    noPenaltyReasonDetails: {
      component: 'Input',
      label: '不处罚原因详情',
      ...textField,
    },
    noPenaltyReason: {
      component: 'DictSelect',
      label: '不处罚原因',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_NoPunishReason?.list || []
          }
          return []
        },
      }),
      mode: 'detail',
    },
    attachment: {
      component: 'Upload',
      label: '',
      visible: true,
      props: {
        oss: OSS_CONFIG,
        clientCode: 'GLOBAL',
        listType: 'text',
        accept:
          'image/jpg,image/jpeg,image/png,application/pdf,application/x-rar-compressed,application/zip',
        fileSizeLimit: 50,
        onChange: (fileList) => {
          if (fileList.length === 10) {
            message.warn('最多支持上传10个文件')
          }
        },
        maxNum: 10,
        remark: '支持扩展名：pdf、jpg、jpeg、png、rar、zip，单个文件不超过 50M',
      },
    },
    // V1.0.7
    actualPayUnit1: {
      component: 'DictSelect',
      label: '实际赔付单位1',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_PAYMENTCO?.list || []
          }
          return []
        },
      }),
      props: {
        mode: 'multiple',
      },
    },
    payType1: {
      component: 'DictSelect',
      label: '赔付险种1',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_PAY_CATEGORY?.list || []
          }
          return []
        },
      }),
    },
    insuranceCompanyPayAmount1: {
      component: 'InputNumber',
      label: '保险公司赔付金额1',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    actualPayUnit2: {
      component: 'DictSelect',
      label: '实际赔付单位2',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_PAYMENTCO?.list || []
          }
          return []
        },
      }),
      props: {
        mode: 'multiple',
      },
    },
    payType2: {
      component: 'DictSelect',
      label: '赔付险种2',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_PAY_CATEGORY?.list || []
          }
          return []
        },
      }),
    },
    insuranceCompanyPayAmount2: {
      component: 'InputNumber',
      label: '保险公司赔付金额2',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    actualPayUnit3: {
      component: 'DictSelect',
      label: '实际赔付单位3',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_PAYMENTCO?.list || []
          }
          return []
        },
      }),
      props: {
        mode: 'multiple',
      },
    },
    payType3: {
      component: 'DictSelect',
      label: '赔付险种3',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_PAY_CATEGORY?.list || []
          }
          return []
        },
      }),
    },
    insuranceCompanyPayAmount3: {
      component: 'InputNumber',
      label: '保险公司赔付金额3',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
  }
  // 字段权限处理
  const authFields = map(
    extra?.ucDict?.[UC_DICT_KEY.SECURITY_AUTH_FIELD] || [],
    (subItem) => subItem.code,
  )
  // 人员组织选择器 展示组织树结构权限
  const hasDeptAuth =
    extra?.ucDict &&
    extra?.ucDict?.[UC_DICT_KEY.DEPT_AUTH].length > 0 &&
    findIndex(extra?.ucDict?.[UC_DICT_KEY.DEPT_AUTH], (item: any) => item?.code === '1') >= 0
  map(keys(schema), (key: string) => {
    if (!authFields.includes(key) && !schema[key].mode) {
      schema[key].mode = 'detail'
      schema[key].required = false
    }

    // 人员组织选择器控制权限
    if (['userIdResponsibleManager', 'userIdShopFollowPrincipal'].includes(key)) {
      schema[key].props = {
        ...schema[key].props,
        onlySearch: !hasDeptAuth,
      }
    }
  })
  return schema
}
