import React from 'react'
import { renderStateCenterCtx, StateCenter } from '@amazebird/utils'
import { useUcPermissionDict } from '@/hooks/useUcPermissionDict'
import { UC_DICT_KEY } from '@/constants'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import Schedule from './components/Schedule'

export default function index() {
  const { data: ehrDict } = useEhrDicts()
  const { data: dhrDict } = useDhrDicts()
  const { data: ucDict, isLoading } = useUcPermissionDict({
    authKey: [UC_DICT_KEY.SECURITY_AUTH_FIELD, UC_DICT_KEY.DEPT_AUTH],
  })

  const bind = [
    {
      name: 'Schedule',
      fields: [
        'contact',
        'positionId',
        'positionType',
        'departmentLevelFourth',
        'timeHire',
        'timeEventCreated',
        'userIdUpdate',
        'timeUpdate',
        'oaNo',
        'id',
        'timeIncidentYearMonthCopy',
        'accidentContent',
        'accidentNatureName',
        'accidentScenario',
        'actualPayUnit',
        'alarmUser',
        'claimPaidDate',
        'claimUser',
        'closeAmount',
        'closeDate',
        'companyRepayAmount',
        'completeClaimMaterialPostDate',
        'currentDebt',
        'deliveryOrderNum',
        'disabilityLevel',
        'employeeRepayAmount',
        'employeeTotalDebt',
        'estimatedAmount',
        'eventCoefficientChangeReason',
        'eventCoefficientName',
        'eventLevelName',
        'injuredPersonnelCondition',
        'insuranceCompanyPayAmount',
        'insuranceRepayAmount',
        'insureType',
        'isApplyLaborAbilityIdentification',
        'isApplyWorkInjuryCompensation',
        'isApplyWorkInjuryRecognition',
        'isBorrow',
        'isRecovery',
        'isReportInsurance',
        'isSignedAttachedBook',
        'isStaffPenalty',
        'isSuspend',
        'isThirdPartyInvolved',
        'isWithdraw',
        'isWorkInjury',
        'notImplementedRewardPunishReason',
        'payType',
        'penaltyTypeName',
        'process',
        'processRemark',
        'pupuPayAmount',
        'recoverAmount',
        'recoverMethod',
        'recoveredAmount',
        'recoveryDetails',
        'remark',
        'remarkOther',
        'remarkOtherTwo',
        'repayProgress',
        'reportUnit',
        'responsibilityAllocationName',
        'rewardPunishCategory',
        'rewardPunishDate',
        'rewardPunishProgress',
        'rewardPunishType',
        'staffInjuryType',
        'staffPayAmount',
        'startRecoverDate',
        'suspendDate',
        'suspendReason',
        'thirdPartyAge',
        'thirdPartyContact',
        'thirdPartyInjuryType',
        'thirdPartyName',
        'thirdPartyPayAmount',
        'thirdPartyPropertyLoss',
        'thirdPartyVehicleLoss',
        'timeAccident',
        'totalBorrowAmount',
        'unclosedReason',
        'userIdResponsibleManager',
        'userIdShopFollowPrincipal',
        'weatherName',
        'withdrawReason',
        'timeIncidentYearMonth',
        'accidentDurationInCompany',
        'accidentDurationCategory',
        'accidentAreaName',
        'eventLocation',
        'dateCategoryName',
        'eventCategoryName',
        'followCycleCategory',
        'accidentContractSubject',
        'accidentContractTypeName',
        'currentContractSubject',
        'currentContractType',
        'accidentDepartmentId',
        'accidentPositionId',
        'followPeriod',
        'followPeriodCategory',
        'is_staff_penalty',
        'noPenaltyReasonDetails',
        'noPenaltyReason',
        'attachment',
        'actualPayUnit1',
        'payType1',
        'insuranceCompanyPayAmount1',
        'actualPayUnit2',
        'payType2',
        'insuranceCompanyPayAmount2',
        'actualPayUnit3',
        'payType3',
        'insuranceCompanyPayAmount3',
      ],
    },
  ]
  const stateCenter = new StateCenter({
    bind,
  })

  if (isLoading) {
    return <div />
  }

  return renderStateCenterCtx(
    stateCenter,
    <>
      <Schedule dict={{ ehrDict, dhrDict, ucDict }} />
    </>,
  )
}
