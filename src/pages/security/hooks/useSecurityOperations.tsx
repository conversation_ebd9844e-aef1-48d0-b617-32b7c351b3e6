import React from 'react'
import { message } from 'antd'
import { useNavigate } from 'react-router-dom'
import { confirmDialog } from '@/components/Dialog'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import CaseModal from '../detail/components/Modals/caseModal'

export enum OperatorType {
  detail = 'detail', // 查看
  compensationApply = 'compensation_apply', // 补偿额度申请
  paymentApply = 'payment_apply', // 安全事件付款申请
  loanApply = 'loan_apply', // 安全事件借款申请
  repaymentApproval = 'repayment_approval', // 回款审批
  noRepaymentApply = 'no_repayment_apply', // 不追偿申请
  upgradeToDispute = 'upgrade_to_dispute', // 升级至安全争议
  close = 'close', // 结案
  withdraw = 'withdraw', // 撤案
  cancelWithdraw = 'cancel_withdraw', // 取消撤案
}

interface UseSecurityOperationsProps {
  record?: any
  dict?: any
}

export const useSecurityOperations = ({ record, dict }: UseSecurityOperationsProps = {}) => {
  const navigate = useNavigate()

  const handleUpgradeToDispute = () => {
    confirmDialog({
      title: '升级至安全争议',
      content: (
        <div>
          确定要将
          <span style={{ fontWeight: 'bold' }}>
            {record?.userName}（{record?.userNum}）
          </span>
          升级至安全争议吗？操作不可逆，请谨慎处理！
        </div>
      ),
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        console.log('upgradeToDispute')
        message.success('升级至安全争议成功')
      },
    })
  }

  const handleClose = () => {
    modalWrapperHoc(CaseModal)({
      type: 'close',
      record,
      onOk: async (values: any) => {
        console.log('close', values)
        message.success('结案成功')
      },
    })
  }

  const handleWithdraw = () => {
    modalWrapperHoc(CaseModal)({
      type: 'withdraw',
      record,
      dict,
      onOk: async (values: any) => {
        console.log('withdraw', values)
        message.success('撤案成功')
      },
    })
  }

  const handleCancelWithdraw = () => {
    confirmDialog({
      title: '取消撤案',
      content: `确定要将${record?.userName}（${record?.userNum}）的事件进行取消撤案？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        console.log('cancelWithdraw')
        message.success('取消撤案成功')
      },
    })
  }

  const handleCompensationApply = () => {
    message.info('补偿额度申请功能开发中...')
  }

  const handlePaymentApply = () => {
    message.info('安全事件付款申请功能开发中...')
  }

  const handleLoanApply = () => {
    message.info('安全事件借款申请功能开发中...')
  }

  const handleRepaymentApproval = () => {
    message.info('回款审批功能开发中...')
  }

  const handleNoRepaymentApply = () => {
    message.info('不追偿申请功能开发中...')
  }

  const operatorClick = (key: string) => {
    switch (key) {
      case OperatorType.detail:
        if (record?.id) {
          navigate(`/security/detail?id=${record.id}`)
        }
        break
      case OperatorType.compensationApply:
        handleCompensationApply()
        break
      case OperatorType.paymentApply:
        handlePaymentApply()
        break
      case OperatorType.loanApply:
        handleLoanApply()
        break
      case OperatorType.repaymentApproval:
        handleRepaymentApproval()
        break
      case OperatorType.noRepaymentApply:
        handleNoRepaymentApply()
        break
      case OperatorType.upgradeToDispute:
        handleUpgradeToDispute()
        break
      case OperatorType.close:
        handleClose()
        break
      case OperatorType.withdraw:
        handleWithdraw()
        break
      case OperatorType.cancelWithdraw:
        handleCancelWithdraw()
        break
      default:
        break
    }
  }

  return {
    operatorClick,
    handleUpgradeToDispute,
    handleClose,
    handleWithdraw,
    handleCancelWithdraw,
    handleCompensationApply,
    handlePaymentApply,
    handleLoanApply,
    handleRepaymentApproval,
    handleNoRepaymentApply,
  }
}
