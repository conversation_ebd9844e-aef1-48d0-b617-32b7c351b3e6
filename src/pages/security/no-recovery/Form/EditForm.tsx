import React, { useMemo } from 'react'
import { getSchema } from '../schema'
import style from '../index.module.less'
import SimpleSecurityForm from '../../components/SimpleSecurityForm'

type IProps = {
  form?: any
  [key: string]: any
}

export default function EditForm(props: IProps) {
  const { ...rest } = props
  const schema = useMemo(() => getSchema(), [])

  const basicFields = [
    { name: 'id' },
    { name: 'user' },
    { name: 'caseAmount' },
    { name: 'companyActualAmount' },
    { name: 'employeeResponsibleAmount' },
    { name: 'employeeActualAmount' },
    { name: 'recoverableAmount' },
    { name: 'recoveredAmount' },
    { name: 'giveUpAmount' },
  ]

  return (
    <SimpleSecurityForm
      {...rest}
      pageName="security-no-recovery"
      schema={schema}
      className={style.noRecoveryForm}
      basicFields={basicFields}
      descriptionField={{ name: 'noRecoveryReason' }}
      attachmentField={{ name: 'attachment' }}
      wrapperClassName={style.innerWrapper}
    />
  )
}
