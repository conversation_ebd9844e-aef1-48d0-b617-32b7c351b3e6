import React from 'react'
import { renderStateCenterCtx, StateCenter } from '@amazebird/utils'
import EditForm from './EditForm'
import DetailForm from './DetailForm'

type IProps = {
  form?: any
  [key: string]: any
}

const bindData = [
  {
    name: 'NoRecoveryForm',
    fields: [
      'id',
      'user',
      'caseAmount',
      'companyActualAmount',
      'employeeResponsibleAmount',
      'employeeActualAmount',
      'recoverableAmount',
      'recoveredAmount',
      'giveUpAmount',
      'noRecoveryReason',
      'attachment',
    ],
  },
]

export function NoRecoveryForm(props: IProps) {
  const initialStates = {}

  const stateCenter = new StateCenter({
    initialStates,
    bindData,
  })

  return renderStateCenterCtx(stateCenter, <EditForm {...props} />)
}

export function NoRecoveryDetailForm(props: IProps) {
  const initialStates = {}

  const stateCenter = new StateCenter({
    initialStates,
    bindData,
  })

  return renderStateCenterCtx(stateCenter, <DetailForm {...props} />)
}
