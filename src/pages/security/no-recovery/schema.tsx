import { SchemaType } from '@amazebird/schema-form'
import { textField, createBaseFieldsSchema, createTextAreaField, attachmentField } from '../utils'

export const getSchema = () => {
  const baseFields = createBaseFieldsSchema()

  const schema: SchemaType = {
    ...baseFields,
    caseAmount: {
      label: '结案金额',
      ...textField,
    },
    companyActualAmount: {
      label: '公司实际支出',
      ...textField,
    },
    employeeResponsibleAmount: {
      label: '员工应承担金额',
      ...textField,
    },
    employeeActualAmount: {
      label: '员工实际承担金额',
      ...textField,
    },
    recoverableAmount: {
      label: '可追偿金额',
      ...textField,
    },
    recoveredAmount: {
      label: '已追回金额',
      ...textField,
    },
    giveUpAmount: {
      label: '放弃追偿金额',
      ...textField,
    },
    noRecoveryReason: createTextAreaField('申请不追偿说明'),
    attachment: {
      ...attachmentField,
      required: true,
    },
  }
  return schema
}
