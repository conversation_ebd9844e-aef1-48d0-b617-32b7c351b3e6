import React from 'react'
import { SchemaType } from '@amazebird/schema-form'
import { isNil, map, filter, isObject } from 'lodash-es'
import moment from 'moment'
import { Typography, message } from 'antd'
import { OSS_CONFIG } from '@/constants'

const { Text } = Typography

type EllipsisTextProps = {
  value?: any
  format?: string
  options?: any[]
  ellipsis?: boolean
  [key: string]: any
}

const EllipsisText = (props: EllipsisTextProps) => {
  const ellipsis = isNil(props?.ellipsis) ? true : props.ellipsis
  let text = props.value
  if (props?.format && text) {
    text = moment(props.value).format(props?.format)
  } else if (props?.options && props.options.length > 0) {
    text = map(
      filter(props?.options, (item) => item.value === props.value),
      (item) => item.label,
    ).join('；')
  } else if (isObject(text) && 'label' in text) {
    text = text.label
  }
  return <Text ellipsis={ellipsis ? { tooltip: text || '--' } : false}>{text || '--'}</Text>
}

const textField = {
  mode: 'detail',
  renderItem: () => (props) => {
    return <EllipsisText {...props} />
  },
}

export const getSchema = () => {
  const schema: SchemaType = {
    id: {
      label: '事件ID',
      ...textField,
    },
    user: {
      label: '事件员工姓名',
      ...textField,
    },
    caseAmount: {
      label: '结案金额',
      ...textField,
    },
    companyActualAmount: {
      label: '公司实际支出',
      ...textField,
    },
    employeeResponsibleAmount: {
      label: '员工应承担金额',
      ...textField,
    },
    employeeActualAmount: {
      label: '员工实际承担金额',
      ...textField,
    },
    recoverableAmount: {
      label: '可追偿金额',
      ...textField,
    },
    recoveredAmount: {
      label: '已追回金额',
      ...textField,
    },
    giveUpAmount: {
      label: '放弃追偿金额',
      ...textField,
    },
    noRecoveryReason: {
      label: '申请不追偿说明',
      component: 'Input.TextArea',
      required: true,
      props: {
        maxLength: 500,
      },
    },
    attachment: {
      component: 'Upload',
      label: '附件',
      visible: true,
      required: true,
      props: {
        oss: OSS_CONFIG,
        clientCode: 'GLOBAL',
        listType: 'text',
        accept:
          'image/jpg,image/jpeg,image/png,application/pdf,application/x-rar-compressed,application/zip',
        fileSizeLimit: 50,
        onChange: (fileList) => {
          if (fileList.length === 10) {
            message.warn('最多支持上传10个文件')
          }
        },
        maxNum: 10,
        remark: '支持扩展名：pdf、jpg、jpeg、png、rar、zip，单个文件不超过 50M',
      },
    },
  }
  return schema
}
