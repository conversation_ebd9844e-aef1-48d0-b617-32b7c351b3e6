import React, { useMemo } from 'react'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import { Card, Row, Col } from 'antd'
import { map } from 'lodash-es'
import { isMobile } from '@galaxy/utils'
import { createFormItemConfig } from '../../utils'
import { getSchema } from '../schema'
import style from '../index.module.less'
import '@amazebird/antd-business-field'

type IProps = {
  form?: any
  [key: string]: any
}

export default function DetailForm(props: IProps) {
  const { ...rest } = props
  const schema = useMemo(() => getSchema(), [])
  const generateFormItemConfig = useMemo(() => createFormItemConfig('security-reimbursement'), [])

  const basicProps = {
    labelCol: { flex: '135px' },
    ...(isMobile() ? { wrapperCol: { flex: '1' } } : { wrapperCol: { span: 17 } }),
  }

  const renderItem = (key: string) => {
    return (
      <Col xs={24} md={12} key={key}>
        <Item {...generateFormItemConfig(key)} {...basicProps} />
      </Col>
    )
  }

  return (
    <SchemaForm
      {...rest}
      schema={schema}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 10 }}
      className={style.reimbursementForm}
    >
      <Card title="基本信息" size="default">
        <Row gutter={0} align="top">
          {map(
            [
              'id',
              'user',
              'timeReimbursement',
              'reimbursementType',
              'insureEstimatedAmount',
              'insuranceActualAmount',
              'employeeResponsibleAmount',
              'employeeRepaymentAmount',
              'employeeReimbursementAmount',
              'employeeActualAmount',
              'recoverableAmount',
              'recoveredAmount',
            ],
            (key) => renderItem(key),
          )}
        </Row>
        <Item {...generateFormItemConfig('expenseDescription')} {...basicProps} />
      </Card>

      <Card title="附件" size="default">
        <Item {...generateFormItemConfig('attachment')} {...basicProps} />
      </Card>
    </SchemaForm>
  )
}
