import React from 'react'
import { renderStateCenterCtx, StateCenter } from '@amazebird/utils'
import EditForm from './EditForm'
import DetailForm from './DetailForm'

type IProps = {
  form?: any
  [key: string]: any
}

const bindData = [
  {
    name: 'ReimbursementForm',
    fields: [
      'id',
      'user',
      'timeReimbursement',
      'reimbursementType',
      'insureEstimatedAmount',
      'insuranceActualAmount',
      'employeeResponsibleAmount',
      'employeeRepaymentAmount',
      'employeeReimbursementAmount',
      'employeeActualAmount',
      'recoverableAmount',
      'recoveredAmount',
      'expenseDescription',
      'attachment',
    ],
  },
]

export function ReimbursementForm(props: IProps) {
  const initialStates = {}

  const stateCenter = new StateCenter({
    initialStates,
    bindData,
  })

  return renderStateCenterCtx(stateCenter, <EditForm {...props} />)
}

export function ReimbursementDetailForm(props: IProps) {
  const initialStates = {}

  const stateCenter = new StateCenter({
    initialStates,
    bindData,
  })

  return renderStateCenterCtx(stateCenter, <DetailForm {...props} />)
}
