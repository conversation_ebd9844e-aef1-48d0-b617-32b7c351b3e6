import React, { useMemo } from 'react'
import { getSchema } from './schema'
import style from './index.module.less'
import SimpleSecurityForm from '../components/SimpleSecurityForm'

const Reimbursement: React.FC = () => {
  const schema = useMemo(() => getSchema(), [])

  const basicFields = [
    { name: 'id' },
    { name: 'user' },
    { name: 'timeReimbursement' },
    { name: 'reimbursementType' },
    { name: 'insureEstimatedAmount' },
    { name: 'insuranceActualAmount' },
    { name: 'employeeResponsibleAmount' },
    { name: 'employeeRepaymentAmount' },
    { name: 'employeeReimbursementAmount' },
    { name: 'employeeActualAmount' },
    { name: 'recoverableAmount' },
    { name: 'recoveredAmount' },
  ]

  return (
    <SimpleSecurityForm
      pageName="security-reimbursement"
      schema={schema}
      className={style.reimbursementForm}
      basicFields={basicFields}
      descriptionField={{ name: 'expenseDescription' }}
      attachmentField={{ name: 'attachment' }}
      wrapperClassName={style.innerWrapper}
    />
  )
}

export default Reimbursement
