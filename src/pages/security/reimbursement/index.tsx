import React, { useMemo } from 'react'
import { SchemaForm, Item, FormItemGrid } from '@amazebird/antd-schema-form'
import { getSchema } from './scheme'
import style from './index.module.less'
import '@amazebird/antd-business-field'
import { createFormItemConfig } from '../utils'

const Reimbursement: React.FC = () => {
  const form = SchemaForm.createForm()
  const schema = useMemo(() => getSchema(), [])
  const generateFormItemConfig = useMemo(() => createFormItemConfig('security-reimbursement'), [])

  return (
    <div>
      <SchemaForm
        schema={schema}
        layout="horizontal"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 10 }}
        form={form}
        className={style.reimbursementForm}
      >
        <div className={style.innerWrapper}>
          <FormItemGrid colCount={2} fillEmpty>
            <Item {...generateFormItemConfig('id')} />
            <Item {...generateFormItemConfig('user')} />
            <Item {...generateFormItemConfig('timeReimbursement')} />
            <Item {...generateFormItemConfig('reimbursementType')} />
            <Item {...generateFormItemConfig('insureEstimatedAmount')} />
            <Item {...generateFormItemConfig('insuranceActualAmount')} />
            <Item {...generateFormItemConfig('employeeResponsibleAmount')} />
            <Item {...generateFormItemConfig('employeeRepaymentAmount')} />
            <Item {...generateFormItemConfig('employeeReimbursementAmount')} />
            <Item {...generateFormItemConfig('employeeActualAmount')} />
            <Item {...generateFormItemConfig('recoverableAmount')} />
            <Item {...generateFormItemConfig('recoveredAmount')} />
          </FormItemGrid>
          <Item
            {...generateFormItemConfig('expenseDescription')}
            wrapperCol={{ span: 17 }}
            labelCol={{ span: 4 }}
          />
          <Item field="attachment" wrapperCol={{ span: 17 }} labelCol={{ span: 4 }} />
        </div>
      </SchemaForm>
    </div>
  )
}

export default Reimbursement
