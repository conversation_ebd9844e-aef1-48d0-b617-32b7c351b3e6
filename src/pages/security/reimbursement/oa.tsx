import React, { useEffect, useRef } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { resolveQuery } from '@galaxy/utils'
import { getEmploymentDisputeOAById } from '@/api/employment-dispute'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import PageLoading from '@/components/pageLoading'
import { ReimbursementDetailForm } from './Form'

const TOOA_EVENT = 'dhrPageCompleted'
const OAReimbursement = () => {
  const form = SchemaForm.createForm()
  const query = resolveQuery()
  const contentRef = useRef<HTMLDivElement>(null)
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()

  const setOaPageHeight = () => {
    window.top?.postMessage(
      {
        action: TOOA_EVENT,
        height: contentRef.current?.offsetHeight || 1000,
      },
      '*',
    )
  }

  const getEmploymentDispute = async () => {
    const res = await getEmploymentDisputeOAById({ id: query?.id })
    const data = res.data || {}
    const values = transformDataToFormData(data)
    form.setFieldsValue({ ...values })
    setTimeout(() => setOaPageHeight())
  }

  useEffect(() => {
    if (!dhrLoading && !ehrLoading) {
      getEmploymentDispute()
      setOaPageHeight()
    }
  }, [ehrLoading, dhrLoading])

  if (ehrLoading || dhrLoading) {
    return <PageLoading style={{ height: '100vh', backgroundColor: '#fff' }} />
  }

  return (
    <div ref={contentRef}>
      <ReimbursementDetailForm form={form} ehrDict={ehrDict} dhrDict={dhrDict} />
    </div>
  )
}

export default OAReimbursement
