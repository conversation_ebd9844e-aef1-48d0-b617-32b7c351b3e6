import { Observer, SchemaType } from '@amazebird/schema-form'
import { textField, numberAmountField, createBaseFieldsSchema, createTextAreaField, attachmentField } from '../utils'

export const getSchema = () => {
  const baseFields = createBaseFieldsSchema()

  const schema: SchemaType = {
    ...baseFields,
    timeReimbursement: {
      label: '回款日期',
      component: 'InputNumber',
      mode: 'detail',
    },
    reimbursementType: {
      label: '回款类型',
      component: 'Select',
      options: [
        { label: '保司回款', value: '1' },
        { label: '员工回款', value: '2' },
        { label: '追偿回款', value: '3' },
      ],
      props: {
        allowClear: true,
      },
    },
    insureEstimatedAmount: {
      label: '保司预估金额',
      component: 'InputNumber',
      mode: 'detail',
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '1'
        },
      }),
    },
    insuranceActualAmount: {
      label: '保险实际赔付金额',
      ...numberAmountField,
      rules: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '1'
            ? [{ required: true, message: '请输入保司预估金额' }]
            : []
        },
      }),
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '1'
        },
      }),
    },
    employeeResponsibleAmount: {
      label: '员工应承担金额',
      component: 'InputNumber',
      mode: 'detail',
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '2' || reimbursementType === '3'
        },
      }),
    },
    employeeRepaymentAmount: {
      label: '员工已还款金额',
      component: 'InputNumber',
      mode: 'detail',
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '2'
        },
      }),
    },
    employeeReimbursementAmount: {
      label: '员工本次回款金额',
      ...numberAmountField,
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '2'
        },
      }),
    },
    employeeActualAmount: {
      label: '员工实际承担金额',
      component: 'InputNumber',
      mode: 'detail',
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '3'
        },
      }),
    },
    recoverableAmount: {
      label: '可追偿金额',
      component: 'InputNumber',
      mode: 'detail',
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '3'
        },
      }),
    },
    recoveredAmount: {
      label: '已追回金额',
      ...numberAmountField,
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '3'
        },
      }),
    },
    expenseDescription: createTextAreaField('费用情况说明'),
    attachment: {
      ...attachmentField,
      required: true,
    },
  }
  return schema
}
