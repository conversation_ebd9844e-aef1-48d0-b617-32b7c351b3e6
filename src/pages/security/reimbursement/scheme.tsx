import React from 'react'
import { Observer, SchemaType } from '@amazebird/schema-form'
import { isNil, map, filter, isObject } from 'lodash-es'
import moment from 'moment'
import { Typography, message } from 'antd'
import { OSS_CONFIG } from '@/constants'

const { Text } = Typography

type EllipsisTextProps = {
  value?: any
  format?: string
  options?: any[]
  ellipsis?: boolean
  [key: string]: any
}

const EllipsisText = (props: EllipsisTextProps) => {
  const ellipsis = isNil(props?.ellipsis) ? true : props.ellipsis
  let text = props.value
  if (props?.format && text) {
    text = moment(props.value).format(props?.format)
  } else if (props?.options && props.options.length > 0) {
    text = map(
      filter(props?.options, (item) => item.value === props.value),
      (item) => item.label,
    ).join('；')
  } else if (isObject(text) && 'label' in text) {
    text = text.label
  }
  return <Text ellipsis={ellipsis ? { tooltip: text || '--' } : false}>{text || '--'}</Text>
}

const textField = {
  mode: 'detail',
  renderItem: () => (props) => {
    return <EllipsisText {...props} />
  },
}

const numberAmountField = {
  component: 'InputNumber',
  max: 10000000,
  min: 0,
  props: {
    addonAfter: '元',
    precision: 2,
  },
  required: true,
}

export const getSchema = () => {
  const schema: SchemaType = {
    id: {
      label: '事件ID',
      ...textField,
    },
    user: {
      label: '事件员工姓名',
      ...textField,
    },
    timeReimbursement: {
      label: '回款日期',
      component: 'InputNumber',
      mode: 'detail',
    },
    reimbursementType: {
      label: '回款类型',
      component: 'Select',
      options: [
        { label: '保司回款', value: '1' },
        { label: '员工回款', value: '2' },
        { label: '追偿回款', value: '3' },
      ],
      props: {
        allowClear: true,
      },
    },
    insureEstimatedAmount: {
      label: '保司预估金额',
      component: 'InputNumber',
      mode: 'detail',
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '1'
        },
      }),
    },
    insuranceActualAmount: {
      label: '保险实际赔付金额',
      ...numberAmountField,
      rules: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '1'
            ? [{ required: true, message: '请输入保司预估金额' }]
            : []
        },
      }),
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '1'
        },
      }),
    },
    employeeResponsibleAmount: {
      label: '员工应承担金额',
      component: 'InputNumber',
      mode: 'detail',
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '2' || reimbursementType === '3'
        },
      }),
    },
    employeeRepaymentAmount: {
      label: '员工已还款金额',
      component: 'InputNumber',
      mode: 'detail',
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '2'
        },
      }),
    },
    employeeReimbursementAmount: {
      label: '员工本次回款金额',
      ...numberAmountField,
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '2'
        },
      }),
    },
    employeeActualAmount: {
      label: '员工实际承担金额',
      component: 'InputNumber',
      mode: 'detail',
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '3'
        },
      }),
    },
    recoverableAmount: {
      label: '可追偿金额',
      component: 'InputNumber',
      mode: 'detail',
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '3'
        },
      }),
    },
    recoveredAmount: {
      label: '已追回金额',
      ...numberAmountField,
      visible: Observer({
        watch: 'reimbursementType',
        action: (reimbursementType) => {
          return reimbursementType === '3'
        },
      }),
    },
    expenseDescription: {
      component: 'Input.TextArea',
      required: true,
      label: '费用情况说明',
      props: {
        maxLength: 500,
      },
    },
    attachment: {
      component: 'Upload',
      label: '附件',
      visible: true,
      required: true,
      props: {
        oss: OSS_CONFIG,
        clientCode: 'GLOBAL',
        listType: 'text',
        accept:
          'image/jpg,image/jpeg,image/png,application/pdf,application/x-rar-compressed,application/zip',
        fileSizeLimit: 50,
        onChange: (fileList) => {
          if (fileList.length === 10) {
            message.warn('最多支持上传10个文件')
          }
        },
        maxNum: 10,
        remark: '支持扩展名：pdf、jpg、jpeg、png、rar、zip，单个文件不超过 50M',
      },
    },
  }
  return schema
}
