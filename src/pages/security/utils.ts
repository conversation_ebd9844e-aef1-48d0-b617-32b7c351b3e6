import { uuid } from '@galaxy/utils'

/** 柯里化函数：先接收pageName，返回携带该pageName的generateFormItemConfig函数 */
export const createFormItemConfig = (pageName: string) => {
  // 表单项class前缀
  const FORM_ITEM_CLASS_PREFIX = `form-item-${pageName}_${uuid()}__`

  /** 生成form item class */
  const generateFormItemClass = (name: string) => `${FORM_ITEM_CLASS_PREFIX}${name}`

  /** 生成item配置 */
  const generateFormItemConfig = (name: string) => ({
    className: generateFormItemClass(name),
    field: name,
  })

  return generateFormItemConfig
}
