import React from 'react'
import { uuid } from '@galaxy/utils'
import { SchemaType } from '@amazebird/schema-form'
import { isNil, map, filter, isObject } from 'lodash-es'
import moment from 'moment'
import { Typography, message } from 'antd'
import { OSS_CONFIG } from '@/constants'

const { Text } = Typography

/** 柯里化函数：先接收pageName，返回携带该pageName的generateFormItemConfig函数 */
export const createFormItemConfig = (pageName: string) => {
  // 表单项class前缀
  const FORM_ITEM_CLASS_PREFIX = `form-item-${pageName}_${uuid()}__`

  /** 生成form item class */
  const generateFormItemClass = (name: string) => `${FORM_ITEM_CLASS_PREFIX}${name}`

  /** 生成item配置 */
  const generateFormItemConfig = (name: string) => ({
    className: generateFormItemClass(name),
    field: name,
  })

  return generateFormItemConfig
}

// 通用组件和配置
type EllipsisTextProps = {
  value?: any
  format?: string
  options?: any[]
  ellipsis?: boolean
  [key: string]: any
}

export const EllipsisText = (props: EllipsisTextProps) => {
  const ellipsis = isNil(props?.ellipsis) ? true : props.ellipsis
  let text = props.value
  if (props?.format && text) {
    text = moment(props.value).format(props?.format)
  } else if (props?.options && props.options.length > 0) {
    text = map(
      filter(props?.options, (item) => item.value === props.value),
      (item) => item.label,
    ).join('；')
  } else if (isObject(text) && 'label' in text) {
    text = text.label
  }
  return <Text ellipsis={ellipsis ? { tooltip: text || '--' } : false}> {text || '--'}</Text>
}

// 通用字段配置
export const textField = {
  mode: 'detail',
  renderItem: () => (props) => {
    return <EllipsisText {...props} />
  },
}

export const numberAmountField = {
  component: 'InputNumber',
  max: 10000000,
  min: 0,
  props: {
    addonAfter: '元',
    precision: 2,
  },
  required: true,
}

// 通用附件上传配置
export const attachmentField = {
  component: 'Upload',
  label: '附件',
  visible: true,
  props: {
    oss: OSS_CONFIG,
    clientCode: 'GLOBAL',
    listType: 'text',
    accept:
      'image/jpg,image/jpeg,image/png,application/pdf,application/x-rar-compressed,application/zip',
    fileSizeLimit: 50,
    onChange: (fileList) => {
      if (fileList.length === 10) {
        message.warn('最多支持上传10个文件')
      }
    },
    maxNum: 10,
    remark: '支持扩展名：pdf、jpg、jpeg、png、rar、zip，单个文件不超过 50M',
  },
}

// 基础字段Schema生成器
export const createBaseFieldsSchema = (): Partial<SchemaType> => ({
  id: {
    label: '事件ID',
    ...textField,
  },
  user: {
    label: '事件员工姓名',
    ...textField,
  },
  attachment: attachmentField,
})

// 文本域字段生成器
export const createTextAreaField = (label: string, required = true, maxLength = 500) => ({
  component: 'Input.TextArea',
  label,
  required,
  props: {
    maxLength,
    showCount: false,
  },
})
