import React from 'react'
import { SettingOutlined } from '@ant-design/icons'

const employeeRelationRoute = {
  path: '', // !: 在主应用下需要加上 employee-relations 层级
  name: '员工关系管理平台',
  icon: <SettingOutlined rev={undefined} />,
  component: '/portal/portal-main',
  routes: [
    {
      name: '安全事件',
      path: `/security`,
      component: '/security/index',
      permission: true,
      routes: [
        {
          name: '安全事件详情',
          path: `/security/detail`,
          component: '/security/detail/index',
          hideInMenu: true,
        },
        {
          name: '赔偿额度申请',
          path: `/security/compensation`,
          component: '/security/compensation/index',
          hideInMenu: true,
        },
        {
          name: '回款审批',
          path: `/security/reimbursement`,
          component: '/security/reimbursement/index',
          hideInMenu: true,
        },
        {
          name: '不追偿申请',
          path: `/security/no-recovery`,
          component: '/security/no-recovery/index',
          hideInMenu: true,
        },
      ],
    },
    {
      name: '用工争议',
      path: '/employment-dispute',
      component: '/employment-dispute/list/index',
      permission: true,
      routes: [
        {
          name: '用工争议详情',
          path: `/employment-dispute/detail`,
          component: '/employment-dispute/detail/index',
          hideInMenu: true,
        },
        {
          name: '赔偿额度申请',
          path: `/employment-dispute/compensation`,
          component: '/employment-dispute/compensation/index',
          hideInMenu: true,
        },
      ],
    },
    // OA嵌入使用
    {
      name: '',
      path: '/employment-dispute-detail',
      component: '/employment-dispute/detail/oa',
      permission: false,
      hideLayout: true,
      hideInMenu: true,
    },
    {
      name: '',
      path: '/security/compensation-oa',
      component: '/security/compensation/oa',
      permission: false,
      hideLayout: true,
      hideInMenu: true,
    },
    {
      name: '',
      path: '/security/reimbursement-oa',
      component: '/security/reimbursement/oa',
      permission: false,
      hideLayout: true,
      hideInMenu: true,
    },
    {
      name: '',
      path: '/security/no-recovery-oa',
      component: '/security/no-recovery/oa',
      permission: false,
      hideLayout: true,
      hideInMenu: true,
    },
  ],
}

export default employeeRelationRoute
